import { DeliveryMethod } from "@shopify/shopify-api";
import db from "../db.server";
import { awardPointsForOrder } from "../services/pointsService.server";
import { upsertCustomerFromShopify } from "../models/Customer.server";

export const POINTS_WEBHOOKS = [
  {
    topic: "ORDERS_CREATE",
    path: "/webhooks/points/order-created",
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    topic: "ORDERS_CANCELLED",
    path: "/webhooks/points/order-cancelled",
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    topic: "ORDERS_FULFILLED",
    path: "/webhooks/points/order-fulfilled",
    deliveryMethod: DeliveryMethod.Http,
  },
];

interface OrderWebhookData {
  id: string;
  customer?: {
    id: string;
  };
  total_price: string;
  cancelled_at?: string;
  fulfillment_status?: string;
}

export async function handleOrderCreated(
  _topic: string,
  shop: string,
  webhookRequestBody: string
) {
  const payload = JSON.parse(webhookRequestBody) as OrderWebhookData;
  console.log("payload", payload);

  if (!payload.customer?.id) return;

  try {
    const amount = parseFloat(payload.total_price);
    const customerId = String(payload.customer.id);

    // Synchroniser le client avec notre base de données
    if (payload.customer) {
      await upsertCustomerFromShopify(payload.customer, shop);
    }

    // Utiliser le service pour attribuer les points
    const result = await awardPointsForOrder(shop, customerId, payload.id, amount);

    if (!result) {
      console.log("Aucun point attribué pour cette commande");
    } else {
      console.log(`${result.points} points attribués au client ${customerId} pour la commande ${payload.id}`);
    }
  } catch (error) {
    console.error("Erreur lors du traitement de la commande :", error);
  }
}

export async function handleOrderCancelled(
  _topic: string,
  _shop: string,
  webhookRequestBody: string
) {
  const payload = JSON.parse(webhookRequestBody) as OrderWebhookData;

  if (!payload.customer?.id) return;

  try {
    const pointsEntry = await db.pointsHistory.findFirst({
      where: {
        metadata: {
          contains: payload.id,
        },
        action: "earn",
      },
      include: {
        customer: true,
      },
    });

    if (!pointsEntry) return;

    await db.customer.update({
      where: {
        id: pointsEntry.ledgerId,
      },
      data: {
        points: {
          decrement: pointsEntry.points,
        },
      },
    });

    await db.pointsHistory.create({
      data: {
        ledgerId: pointsEntry.ledgerId,
        action: "cancel",
        points: -pointsEntry.points,
        description: `Points annulés pour la commande #${payload.id}`,
        metadata: JSON.stringify({
          orderId: payload.id,
          cancelledAt: payload.cancelled_at,
        }),
      },
    });
  } catch (error) {
    console.error("Erreur lors de l'annulation des points :", error);
  }
}

export async function handleOrderFulfilled(
  _topic: string,
  _shop: string,
  webhookRequestBody: string
) {
  const payload = JSON.parse(webhookRequestBody) as OrderWebhookData;

  if (!payload.customer?.id) return;

  try {
    const pointsEntry = await db.pointsHistory.findFirst({
      where: {
        metadata: {
          contains: payload.id,
        },
        action: "validate",
      },
    });

    if (pointsEntry) return;

    const originalEntry = await db.pointsHistory.findFirst({
      where: {
        metadata: {
          contains: payload.id,
        },
        action: "earn",
      },
    });

    if (originalEntry) {
      await db.pointsHistory.create({
        data: {
          ledgerId: originalEntry.ledgerId,
          action: "validate",
          points: 0,
          description: `Points validés pour la commande #${payload.id}`,
          metadata: JSON.stringify({
            orderId: payload.id,
            fulfilledAt: new Date().toISOString(),
          }),
        },
      });
    }
  } catch (error) {
    console.error("Erreur lors de la validation des points :", error);
  }
}
