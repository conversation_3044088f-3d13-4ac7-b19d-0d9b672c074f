<!-- Chargement des assets CSS et JS -->
{{ 'loyalty-widget.css' | asset_url | stylesheet_tag }}

<!-- Widget de fidélité flottant -->
<div id="loyalty-widget" class="loyalty-widget-container"
     data-shop="{{ shop.domain }}"
     data-customer-id="{% if customer %}{{ customer.id }}{% endif %}"
     data-position="{{ block.settings.position }}"
     data-primary-color="{{ block.settings.primary_color }}"
     data-secondary-color="{{ block.settings.secondary_color }}"
     data-show-on-mobile="{{ block.settings.show_on_mobile }}"
     data-auto-open="{{ block.settings.auto_open }}">
  <!-- Bouton flottant principal -->
  <div id="loyalty-trigger" class="loyalty-trigger">
    <div class="loyalty-trigger-content">
      <div class="loyalty-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
        </svg>
      </div>
      <div class="loyalty-points-preview" id="points-preview">
        <span class="points-count">0</span>
        <span class="points-label">pts</span>
      </div>
    </div>
    <div class="loyalty-pulse"></div>
  </div>

  <!-- Panel principal du widget -->
  <div id="loyalty-panel" class="loyalty-panel">
    <div class="loyalty-panel-header">
      <div class="loyalty-header-content">
        <h3 class="loyalty-title" id="loyalty-program-title">Programme de Fidélité</h3>
        <button id="loyalty-close" class="loyalty-close-btn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </button>
      </div>
    </div>

    <div class="loyalty-panel-body">
      <!-- État de chargement -->
      <div id="loyalty-loading" class="loyalty-loading">
        <div class="loyalty-spinner"></div>
        <p>Chargement de vos points...</p>
      </div>

      <!-- État non connecté -->
      <div id="loyalty-guest" class="loyalty-guest " style="display: none;">
        <div class="loyalty-guest-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M8 14s1.5 2 4 2 4-2 4-2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <path d="M9 9h.01M15 9h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </div>
        <h4 id="loyalty-program-name">Rejoignez notre programme !</h4>
        <p id="loyalty-program-description">Gagnez des points à chaque achat et débloquez des récompenses exclusives.</p>
        {% if customer %}
          <button class="loyalty-btn loyalty-btn-primary" onclick="loyaltyWidget.signupToLoyalty()">
            Rejoindre le programme
          </button>
        {% else %}
          <a href="/account/login" class="loyalty-btn loyalty-btn-primary">
            Se connecter
          </a>
        {% endif %}
      </div>

      <!-- État connecté -->
      <div id="loyalty-member" class="loyalty-member" style="display: none;">
        <!-- Informations du client -->
        <div class="loyalty-customer-info">
          <div class="loyalty-avatar">
            <span id="customer-initials">?</span>
          </div>
          <div class="loyalty-customer-details">
            <h4 id="customer-name">Client</h4>
            <span id="customer-status" class="loyalty-status-badge">Invité</span>
          </div>
        </div>

        <!-- Points et statistiques -->
        <div class="loyalty-stats">
          <div class="loyalty-stat-card loyalty-stat-primary">
            <div class="loyalty-stat-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
              </svg>
            </div>
            <div class="loyalty-stat-content">
              <span class="loyalty-stat-value" id="customer-points">0</span>
              <span class="loyalty-stat-label">Points</span>
            </div>
          </div>

          <div class="loyalty-stat-card">
            <div class="loyalty-stat-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 11V7a4 4 0 0 0-8 0v4H6a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-2z" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <div class="loyalty-stat-content">
              <span class="loyalty-stat-value" id="customer-orders">0</span>
              <span class="loyalty-stat-label">Commandes</span>
            </div>
          </div>
        </div>

        <!-- Progression vers la prochaine récompense -->
        <div class="loyalty-progress">
          <div class="loyalty-progress-header">
            <span class="loyalty-progress-title">Prochaine récompense</span>
            <span class="loyalty-progress-remaining" id="points-needed">500 points</span>
          </div>
          <div class="loyalty-progress-bar">
            <div class="loyalty-progress-fill" id="progress-fill" style="width: 0%"></div>
          </div>
          <p class="loyalty-progress-text">Continuez vos achats pour débloquer votre prochaine récompense !</p>
        </div>

        <!-- Actions rapides -->
        <div class="loyalty-actions">
          <button class="loyalty-btn loyalty-btn-outline" id="view-rewards">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" stroke="currentColor" stroke-width="2"/>
              <polyline points="3.27,6.96 12,12.01 20.73,6.96" stroke="currentColor" stroke-width="2"/>
              <line x1="12" y1="22.08" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
            </svg>
            Voir les récompenses
          </button>

          <button class="loyalty-btn loyalty-btn-outline" id="view-history">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
              <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
            </svg>
            Historique
          </button>
        </div>

        <!-- Section principale - Comment gagner des points -->
        <div class="loyalty-main-section" id="loyalty-main-section">
          <div class="loyalty-earn-ways">
            <h5>Comment gagner des points</h5>
            <div class="loyalty-earn-list" id="earn-ways">
              <!-- Sera rempli dynamiquement -->
            </div>
          </div>

          <!-- Section Parrainage -->
          <div class="loyalty-referral-section">
            <div class="loyalty-referral-card">
              <div class="loyalty-referral-header">
                <h6>Parrainez vos amis</h6>
                <span class="loyalty-referral-count" id="referral-count">0 parrainages réalisés</span>
              </div>
              <p class="loyalty-referral-description">
                Partagez ce lien pour offrir à vos amis une réduction de 4€ et gagner des points
              </p>
              <div class="loyalty-referral-link-container">
                <input type="text" class="loyalty-referral-input" id="referral-link" readonly
                       value="https://refs.cc/NnoKl5oL?smile_ref" />
                <button class="loyalty-copy-btn" id="copy-referral-link">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                  </svg>
                </button>
              </div>
              <div class="loyalty-social-share">
                <button class="loyalty-social-btn loyalty-facebook-btn" id="share-facebook">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                  Facebook
                </button>
                <button class="loyalty-social-btn loyalty-twitter-btn" id="share-twitter">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                  X
                </button>
                <button class="loyalty-social-btn loyalty-email-btn" id="share-email">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  Email
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Section Récompenses -->
        <div class="loyalty-rewards-section" id="loyalty-rewards-section" style="display: none;">
          <div class="loyalty-section-header">
            <button class="loyalty-back-btn" id="back-from-rewards">
              <span class="loyalty-back-icon">←</span>
              Retour
            </button>
            <h5>🎁 Récompenses disponibles</h5>
          </div>
          <div class="loyalty-rewards-list" id="rewards-list">
            <!-- Sera rempli dynamiquement -->
          </div>
        </div>

        <!-- Section Historique -->
        <div class="loyalty-history-section" id="loyalty-history-section" style="display: none;">
          <div class="loyalty-section-header">
            <button class="loyalty-back-btn" id="back-from-history">
              <span class="loyalty-back-icon">←</span>
              Retour
            </button>
            <h5>📋 Historique des points</h5>
          </div>
          <div class="loyalty-history-list" id="history-list">
            <!-- Sera rempli dynamiquement -->
          </div>
        </div>
      </div>

      <!-- État d'erreur -->
      <div id="loyalty-error" class="loyalty-error" style="display: none;">
        <div class="loyalty-error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
            <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h4>Oups ! Une erreur est survenue</h4>
        <p>Impossible de charger vos informations de fidélité.</p>
        <button class="loyalty-btn loyalty-btn-outline" onclick="loyaltyWidget.reload()">
          Réessayer
        </button>
      </div>
    </div>
  </div>

  <!-- Overlay -->
  <div id="loyalty-overlay" class="loyalty-overlay"></div>
</div>

<!-- Chargement du JavaScript -->
<script>
  // Configuration du widget depuis Liquid
  window.loyaltyWidgetConfig = {
    shop: {{ shop.domain | json }},
    customerId: {% if customer %}{{ customer.id | json }}{% else %}null{% endif %},
    customerEmail: {% if customer %}{{ customer.email | json }}{% else %}null{% endif %},
    customerFirstName: {% if customer %}{{ customer.first_name | json }}{% else %}null{% endif %},
    customerLastName: {% if customer %}{{ customer.last_name | json }}{% else %}null{% endif %},
    position: {{ block.settings.position | json }},
    primaryColor: {{ block.settings.primary_color | json }},
    secondaryColor: {{ block.settings.secondary_color | json }},
    showOnMobile: {{ block.settings.show_on_mobile | json }},
    autoOpen: {{ block.settings.auto_open | json }},
    // Pas besoin d'apiUrl, on utilise l'App Proxy
  };
</script>

<script src="{{ 'loyalty-widget.js' | asset_url }}" defer></script>

{% schema %}
{
  "name": "Custom Rewards App",
  "target": "body",
  "settings": [
    {
      "type": "header",
      "content": "Configuration du widget"
    },
    {
      "type": "select",
      "id": "position",
      "label": "Position du widget",
      "options": [
        {
          "value": "bottom-right",
          "label": "Bas droite"
        },
        {
          "value": "bottom-left",
          "label": "Bas gauche"
        },
        {
          "value": "top-right",
          "label": "Haut droite"
        },
        {
          "value": "top-left",
          "label": "Haut gauche"
        }
      ],
      "default": "bottom-right"
    },
    {
      "type": "color",
      "id": "primary_color",
      "label": "Couleur principale",
      "default": "#2E7D32"
    },
    {
      "type": "color",
      "id": "secondary_color",
      "label": "Couleur secondaire",
      "default": "#4CAF50"
    },
    {
      "type": "checkbox",
      "id": "show_on_mobile",
      "label": "Afficher sur mobile",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "auto_open",
      "label": "Ouverture automatique pour nouveaux visiteurs",
      "default": false
    }
  ]
}
{% endschema %}
