import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  DataTable,
  Badge,
  Text,
  BlockStack,
  Button,
  Pagination,
  TextField,
  Select,
  Filters,
  ButtonGroup
} from "@shopify/polaris";
import { useState, useCallback } from "react";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { authenticate } from "app/shopify.server";
import { getAllCustomers } from "app/models/Customer.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const url = new URL(request.url);
  
  const page = parseInt(url.searchParams.get("page") || "1");
  const type = url.searchParams.get("type") || "";
  const search = url.searchParams.get("search") || "";

  const result = await getAllCustomers(session.shop, page, 20);

  return json({
    ...result,
    filters: { type, search }
  });
};

export default function Customers() {
  const { customers, total, pages, currentPage, filters } = useLoaderData<typeof loader>();
  
  const [searchValue, setSearchValue] = useState(filters.search);
  const [typeFilter, setTypeFilter] = useState(filters.type);

  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);
  }, []);

  const handleTypeFilterChange = useCallback((value: string) => {
    setTypeFilter(value);
  }, []);

  const clearFilters = useCallback(() => {
    setSearchValue("");
    setTypeFilter("");
  }, []);

  const formatCustomerName = (customer: any) => {
    const firstName = customer.firstName || "";
    const lastName = customer.lastName || "";
    if (firstName || lastName) {
      return `${firstName} ${lastName}`.trim();
    }
    return customer.email || `Client ${customer.customerId}`;
  };

  const rows = customers.map((customer: any) => [
    <Link 
      key={customer.id} 
      to={`/app/customers/${customer.id}`}
      style={{ textDecoration: "none", color: "inherit" }}
    >
      <Text as="span" fontWeight="medium">
        {formatCustomerName(customer)}
      </Text>
    </Link>,
    <Badge tone={customer.type === "member" ? "success" : "info"}>
      {customer.type === "member" ? "Membre" : "Invité"}
    </Badge>,
    `${customer.points} points`,
    `${customer._count?.referrals || 0} personne(s)`,
    customer.email || "—",
    new Date(customer.joinedAt).toLocaleDateString("fr-FR")
  ]);

  const appliedFilters = [];
  if (typeFilter) {
    appliedFilters.push({
      key: "type",
      label: `Type: ${typeFilter === "member" ? "Membre" : "Invité"}`,
      onRemove: () => setTypeFilter("")
    });
  }
  if (searchValue) {
    appliedFilters.push({
      key: "search",
      label: `Recherche: ${searchValue}`,
      onRemove: () => setSearchValue("")
    });
  }

  const filters_components = [
    {
      key: "type",
      label: "Type de client",
      filter: (
        <Select
          label="Type"
          options={[
            { label: "Tous", value: "" },
            { label: "Membres", value: "member" },
            { label: "Invités", value: "guest" }
          ]}
          value={typeFilter}
          onChange={handleTypeFilterChange}
        />
      )
    }
  ];

  return (
    <AdminLayout title="Clients">
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                <Text as="h2" variant="headingMd">
                  Clients du programme de fidélité
                </Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  {total} client(s) au total
                </Text>
              </div>

              <Filters
                queryValue={searchValue}
                queryPlaceholder="Rechercher par nom ou email"
                filters={filters_components}
                appliedFilters={appliedFilters}
                onQueryChange={handleSearchChange}
                onQueryClear={() => setSearchValue("")}
                onClearAll={clearFilters}
              />

              <DataTable
                columnContentTypes={["text", "text", "text", "text", "text", "text"]}
                headings={[
                  "Client",
                  "Type",
                  "Points",
                  "Parrainages",
                  "Email",
                  "Inscrit le"
                ]}
                rows={rows}
                footerContent={
                  pages > 1 ? (
                    <div style={{ display: "flex", justifyContent: "center", padding: "16px" }}>
                      <Pagination
                        hasPrevious={currentPage > 1}
                        onPrevious={() => {
                          // Navigation handled by Link
                        }}
                        hasNext={currentPage < pages}
                        onNext={() => {
                          // Navigation handled by Link
                        }}
                        label={`Page ${currentPage} sur ${pages}`}
                      />
                    </div>
                  ) : undefined
                }
              />
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
}
