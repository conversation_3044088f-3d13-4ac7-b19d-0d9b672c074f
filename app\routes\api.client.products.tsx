import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getExchangeableProducts } from "../models/ExchangeableProducts.server";
import { getSiteSettings } from "../models/SiteSettings.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const url = new URL(request.url);
    const shop = url.searchParams.get('shop');
    
    if (!shop) {
      return json({ error: "Shop parameter required" }, { status: 400 });
    }

    // Récupérer les produits échangeables et les paramètres du programme
    const [exchangeableProducts, settings] = await Promise.all([
      getExchangeableProducts(shop),
      getSiteSettings(shop)
    ]);

    // Calculer le coût en points basé sur les paramètres du programme
    const productsWithPoints = exchangeableProducts.map(product => ({
      ...product,
      pointsCost: settings?.redemptionRate ? Math.ceil(parseFloat(product.price) / settings.redemptionRate) : product.pointsCost
    }));

    return json({ 
      products: productsWithPoints,
      programInfo: {
        pointsName: settings?.pointsName || 'Points',
        redemptionRate: settings?.redemptionRate || 0.01
      }
    });
  } catch (error) {
    console.error("Error loading client products:", error);
    return json({ 
      products: [], 
      programInfo: { pointsName: 'Points', redemptionRate: 0.01 },
      error: "Erreur lors du chargement" 
    }, { status: 500 });
  }
};
