import prisma from "../db.server";
import { upsertCustomerFromShopify } from "../models/Customer.server";

/**
 * Service pour gérer l'attribution de points selon les "Ways to Earn"
 */

export async function awardPointsForSignup(shop: string, customerId: string) {
  try {
    // Récupérer la façon de gagner des points pour l'inscription
    const signupWayToEarn = await prisma.wayToEarn.findFirst({
      where: {
        shop,
        actionType: "signup",
        isActive: true
      },
    });

    if (!signupWayToEarn) {
      console.log("Aucune façon de gagner des points pour l'inscription trouvée");
      return null;
    }

    let points = 0;
    if (signupWayToEarn.earningType === "fixed") {
      points = signupWayToEarn.earningValue;
    } else {
      // Pour l'inscription, on ne peut pas avoir d'incréments basés sur un montant
      console.log("Type d'inscription invalide : increments non supporté pour l'inscription");
      return null;
    }

    // Vérifier si le client a déjà reçu des points d'inscription
    const existingSignupPoints = await prisma.pointsHistory.findFirst({
      where: {
        customer: {
          customerId,
          shop
        },
        action: "signup"
      }
    });

    if (existingSignupPoints) {
      console.log("Le client a déjà reçu des points d'inscription");
      return null;
    }

    // Créer ou mettre à jour le solde de points du client
    const customer = await prisma.customer.upsert({
      where: {
        customerId_shop: {
          customerId,
          shop,
        },
      },
      create: {
        shop,
        customerId,
        points: points,
        type: "member" // Devient membre en s'inscrivant
      },
      update: {
        points: {
          increment: points,
        },
        type: "member" // Devient membre en s'inscrivant
      },
    });

    // Créer l'historique
    await prisma.pointsHistory.create({
      data: {
        ledgerId: customer.id,
        action: "signup",
        points: points,
        description: `Points de bienvenue pour l'inscription`,
        metadata: JSON.stringify({
          wayToEarnId: signupWayToEarn.id,
          actionType: "signup"
        }),
      },
    });

    return {
      points,
      customer
    };
  } catch (error) {
    console.error("Erreur lors de l'attribution des points d'inscription :", error);
    return null;
  }
}

export async function awardPointsForOrder(shop: string, customerId: string, orderId: string, amount: number) {
  try {
    // Récupérer la façon de gagner des points pour les commandes
    const orderWayToEarn = await prisma.wayToEarn.findFirst({
      where: {
        shop,
        actionType: "order",
        isActive: true
      },
    });

    if (!orderWayToEarn) {
      console.log("Aucune façon de gagner des points pour les commandes trouvée");
      return null;
    }

    let points = 0;
    if (orderWayToEarn.earningType === "increments") {
      // Points par euro dépensé
      points = Math.round(amount * orderWayToEarn.earningValue);
    } else {
      // Points fixes par commande
      points = orderWayToEarn.earningValue;
    }

    // Créer ou mettre à jour le solde de points du client
    const customer = await prisma.customer.upsert({
      where: {
        customerId_shop: {
          customerId,
          shop,
        },
      },
      create: {
        shop,
        customerId,
        points: points,
        type: "guest" // Par défaut guest pour les commandes
      },
      update: {
        points: {
          increment: points,
        },
        totalSpent: {
          increment: amount
        },
        ordersCount: {
          increment: 1
        },
        lastOrderAt: new Date()
      },
    });

    // Créer l'historique
    await prisma.pointsHistory.create({
      data: {
        ledgerId: customer.id,
        action: "earn",
        points: points,
        description: `Points gagnés pour la commande #${orderId}`,
        metadata: JSON.stringify({
          orderId: orderId,
          amount: amount,
          wayToEarnId: orderWayToEarn.id,
          actionType: "order"
        }),
      },
    });

    return {
      points,
      customer
    };
  } catch (error) {
    console.error("Erreur lors de l'attribution des points de commande :", error);
    return null;
  }
}

export async function getCustomerPoints(shop: string, customerId: string) {
  try {
    return await prisma.customer.findUnique({
      where: {
        customerId_shop: {
          customerId,
          shop,
        },
      },
      include: {
        history: {
          orderBy: {
            timestamp: 'desc'
          },
          take: 10
        }
      }
    });
  } catch (error) {
    console.error("Erreur lors de la récupération des points client :", error);
    return null;
  }
}
