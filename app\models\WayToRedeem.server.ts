import prisma from "../db.server";

export interface WayToRedeem {
  id: string;
  shop: string;
  name: string;
  description: string;
  redeemType: "discount" | "product" | "shipping";
  redeemValue: number;
  pointsCost: number;
  icon: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateWayToRedeemData {
  name: string;
  description: string;
  redeemType: "discount" | "product" | "shipping";
  redeemValue: number;
  pointsCost: number;
  icon?: string;
  isActive?: boolean;
}

export interface UpdateWayToRedeemData extends Partial<CreateWayToRedeemData> {}

export async function getWaysToRedeem(shop: string) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return [];
    }

    return await prisma.wayToRedeem.findMany({
      where: { shop },
      orderBy: { createdAt: "desc" }
    });
  } catch (error) {
    console.error("Error fetching ways to redeem:", error);
    return [];
  }
}

export async function getWayToRedeemById(id: string, shop: string) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    return await prisma.wayToRedeem.findFirst({
      where: { id, shop }
    });
  } catch (error) {
    console.error("Error fetching way to redeem:", error);
    return null;
  }
}

export async function createWayToRedeem(shop: string, data: CreateWayToRedeemData) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    return await prisma.wayToRedeem.create({
      data: {
        shop,
        name: data.name,
        description: data.description,
        redeemType: data.redeemType,
        redeemValue: data.redeemValue,
        pointsCost: data.pointsCost,
        icon: data.icon || "discount",
        isActive: data.isActive ?? true
      }
    });
  } catch (error) {
    console.error("Error creating way to redeem:", error);
    return null;
  }
}

export async function updateWayToRedeem(id: string, shop: string, data: UpdateWayToRedeemData) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    return await prisma.wayToRedeem.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      }
    });
  } catch (error) {
    console.error("Error updating way to redeem:", error);
    return null;
  }
}

export async function deleteWayToRedeem(id: string, shop: string) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return false;
    }

    await prisma.wayToRedeem.delete({
      where: { id }
    });
    return true;
  } catch (error) {
    console.error("Error deleting way to redeem:", error);
    return false;
  }
}

export async function initializeDefaultWayToRedeem(shop: string) {
  try {
    const existingWays = await getWaysToRedeem(shop);
    if (existingWays.length > 0) {
      return existingWays;
    }

    // Créer le way to redeem par défaut "Order discount"
    const defaultWay = await createWayToRedeem(shop, {
      name: "Order discount",
      description: "Get discount on your orders",
      redeemType: "discount",
      redeemValue: 5, // 5€ de réduction
      pointsCost: 500, // Coûte 500 points
      icon: "discount",
      isActive: true
    });

    return defaultWay ? [defaultWay] : [];
  } catch (error) {
    console.error("Error initializing default way to redeem:", error);
    return [];
  }
}
