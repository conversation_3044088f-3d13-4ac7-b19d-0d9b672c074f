import React, { useState, useCallback } from 'react';
import { Box, Text, TextField, Button, Popover, ActionList } from '@shopify/polaris';

interface ColorPickerProps {
  label: string;
  value: string;
  onChange: (color: string) => void;
  helpText?: string;
}

const PRESET_COLORS = [
  '#FFFFFF',
  '#2E7D32', '#4CAF50', '#8BC34A', '#CDDC39',
  '#FFC107', '#FF9800', '#FF5722', '#F44336',
  '#E91E63', '#9C27B0', '#673AB7', '#3F51B5',
  '#2196F3', '#03A9F4', '#00BCD4', '#009688',
  '#795548', '#9E9E9E', '#607D8B', '#000000'
];

export function ColorPicker({ label, value, onChange, helpText }: ColorPickerProps) {
  const [popoverActive, setPopoverActive] = useState(false);
  const [customColor, setCustomColor] = useState(value);

  const togglePopover = useCallback(() => {
    setPopoverActive(!popoverActive);
  }, [popoverActive]);

  const handleColorSelect = useCallback((color: string) => {
    onChange(color);
    setCustomColor(color);
    setPopoverActive(false);
  }, [onChange]);

  const handleCustomColorChange = useCallback((color: string) => {
    setCustomColor(color);
    // Valider le format hex
    if (/^#[0-9A-F]{6}$/i.test(color)) {
      onChange(color);
    }
  }, [onChange]);

  const activator = (
    <Button onClick={togglePopover} disclosure>
      <Box style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div
          style={{
            width: '20px',
            height: '20px',
            backgroundColor: value,
            border: '1px solid #ccc',
            borderRadius: '4px'
          }}
        />
        <Text as="span">{value}</Text>
      </Box>
    </Button>
  );

  return (
    <Box>
      <Text as="label" variant="bodyMd" fontWeight="medium">
        {label}
      </Text>
      <Box paddingBlockStart="200">
        <Popover
          active={popoverActive}
          activator={activator}
          onClose={togglePopover}
          ariaHaspopup={false}
          sectioned
        >
          <Box padding="400">
            <Text as="h3" variant="headingSm">
              Couleurs prédéfinies
            </Text>
            <Box paddingBlockStart="200">
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(5, 1fr)',
                gap: '8px',
                marginBottom: '16px'
              }}>
                {PRESET_COLORS.map((color) => (
                  <button
                    key={color}
                    onClick={() => handleColorSelect(color)}
                    style={{
                      width: '32px',
                      height: '32px',
                      backgroundColor: color,
                      border: value === color ? '2px solid #000' : '1px solid #ccc',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      padding: 0
                    }}
                    title={color}
                  />
                ))}
              </div>
            </Box>

            <Text as="h3" variant="headingSm">
              Couleur personnalisée
            </Text>
            <Box paddingBlockStart="200">
              <TextField
                label=""
                value={customColor}
                onChange={handleCustomColorChange}
                placeholder="#000000"
                autoComplete="off"
                prefix="#"
              />
              <Box paddingBlockStart="200">
                <Button
                  onClick={() => handleColorSelect(customColor)}
                  disabled={!/^#[0-9A-F]{6}$/i.test(customColor)}
                  size="slim"
                >
                  Appliquer
                </Button>
              </Box>
            </Box>
          </Box>
        </Popover>
      </Box>
      {helpText && (
        <Box paddingBlockStart="100">
          <Text as="p" variant="bodySm" tone="subdued">
            {helpText}
          </Text>
        </Box>
      )}
    </Box>
  );
}
