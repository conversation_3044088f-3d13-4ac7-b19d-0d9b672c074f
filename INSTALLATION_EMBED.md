# Guide d'installation - Interface Client du Programme de Fidélité

## 🎯 Vue d'ensemble

L'interface client du programme de fidélité est maintenant implémentée avec une **Shopify Theme Extension** qui offre :

1. **Widget flottant** avec design extraordinaire et animations fluides
2. **Intégration native** dans tous les thèmes Shopify
3. **Configuration avancée** via l'éditeur de thème
4. **Responsive design** optimisé pour mobile et desktop

## 🎨 Installation de la Theme Extension

### Activation du widget

1. **Accédez à votre admin Shopify**
   - Allez dans `Boutique en ligne` → `Thèmes`
   - Cliquez sur `Personnaliser` sur votre thème actif

2. **Ajoutez le bloc Programme de Fidélité**
   - Dans l'éditeur de thème, cliquez sur `Ajouter un bloc` ou `Add block`
   - Trouvez "Programme de Fidélité" dans la liste des blocs d'app
   - Ajoutez-le à votre thème

3. **Configuration avancée**
   - **Position** : Choisissez où placer le widget (bas droite, bas gauche, etc.)
   - **Couleurs** : Personnalisez les couleurs principale et secondaire
   - **Affichage mobile** : Activez/désactivez sur mobile
   - **Ouverture automatique** : Pour les nouveaux visiteurs

### Avantages de la Theme Extension
- ✅ **Design extraordinaire** avec animations fluides
- ✅ **Widget flottant** non-intrusif
- ✅ **Configuration visuelle** dans l'éditeur de thème
- ✅ **Performance optimisée** avec chargement asynchrone
- ✅ **Responsive parfait** sur tous les appareils

## 🌐 App Proxy - Communication Backend

### Configuration automatique
L'App Proxy est utilisé pour la communication entre le widget et le backend :
- **URL de base** : `https://votre-boutique.myshopify.com/apps/proxy`
- **Endpoints disponibles** :
  - `/apps/proxy/customer` - Données client
  - `/apps/proxy/ways-to-earn` - Façons de gagner des points
  - `/apps/proxy/ways-to-redeem` - Façons d'échanger des points
  - `/apps/proxy/signup` - Inscription au programme

### Avantages de l'App Proxy
- ✅ **URL stable** qui ne change jamais
- ✅ **Authentification automatique** des clients Shopify
- ✅ **Sécurité renforcée** avec validation des signatures
- ✅ **Performance optimisée** sans CORS

## 🎨 Personnalisation du design

### Variables CSS disponibles
Vous pouvez personnaliser l'apparence en ajoutant du CSS dans votre thème :

```css
/* Personnalisation du widget de fidélité */
.loyalty-widget {
  --primary-color: #2e7d32;
  --secondary-color: #f8f9fa;
  --text-color: #333;
  --border-radius: 8px;
}

/* Couleurs personnalisées */
.loyalty-widget .points-value {
  color: var(--primary-color);
}

.loyalty-widget .loyalty-button {
  background: var(--primary-color);
}
```

### Positionnement de l'embed
Dans l'éditeur de thème, vous pouvez :
- Choisir la position (header, footer, sidebar)
- Définir les pages d'affichage
- Ajuster l'espacement et les marges

## 🔧 Configuration technique

### Paramètres requis
L'interface client récupère automatiquement :
- **Shop** : Nom de la boutique
- **Customer ID** : ID du client connecté (si connecté)
- **Customer data** : Points, statut, historique

### Sécurité
- ✅ Vérification de l'origine des requêtes
- ✅ CORS configuré pour Shopify
- ✅ Données client sécurisées
- ✅ Pas d'exposition d'informations sensibles

## 📊 Fonctionnalités disponibles

### Widget compact (App Embed)
- Affichage du solde de points
- Statut du client (Membre/Invité)
- Bouton d'accès aux récompenses
- Message de connexion si non connecté

### Page complète (App Proxy)
- Vue d'ensemble complète du programme
- Liste des récompenses disponibles
- Historique des points (à venir)
- Système d'échange (à venir)

## 🚀 Prochaines fonctionnalités

### En développement
- [ ] **Historique détaillé** des points gagnés/dépensés
- [ ] **Système d'échange** de récompenses en temps réel
- [ ] **Codes de parrainage** personnalisés
- [ ] **Notifications** en temps réel
- [ ] **Gamification** avec badges et niveaux

### Personnalisation avancée
- [ ] **Thèmes** multiples pour le widget
- [ ] **Animations** et transitions
- [ ] **Intégration** avec d'autres apps
- [ ] **Analytics** détaillées côté client

## 📞 Support

### Problèmes courants

**Le widget ne s'affiche pas :**
1. Vérifiez que l'app embed est activé dans le thème
2. Assurez-vous que l'app est installée et configurée
3. Vérifiez les paramètres de visibilité des pages

**Données client non chargées :**
1. Le client doit être connecté à son compte
2. Vérifiez que l'app a les permissions nécessaires
3. Consultez les logs de l'application

**Problèmes de style :**
1. Vérifiez les conflits CSS avec le thème
2. Utilisez les variables CSS pour la personnalisation
3. Testez sur différents appareils et navigateurs

### Contact
Pour toute assistance technique, contactez l'équipe de développement avec :
- URL de la boutique
- Description du problème
- Captures d'écran si applicable
- Console du navigateur (F12) pour les erreurs JavaScript
