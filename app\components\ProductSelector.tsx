import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  Text,
  <PERSON>ton,
  Card,
  BlockStack,
  InlineStack,
  Badge,
  TextField,
  Modal,
  ResourceList,
  ResourceItem,
  Thumbnail,
  Checkbox,
  Spinner
} from '@shopify/polaris';

interface Product {
  id: string;
  title: string;
  handle: string;
  image?: string;
  price: string;
  pointsCost: number;
}

interface ProductSelectorProps {
  selectedProducts: Product[];
  onProductsChange: (products: Product[]) => void;
}

export function ProductSelector({ selectedProducts, onProductsChange }: ProductSelectorProps) {
  const [modalActive, setModalActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [availableProducts, setAvailableProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Fonction pour récupérer les produits depuis l'API
  const fetchProducts = useCallback(async (search?: string) => {
    setLoading(true);
    try {
      const url = search
        ? `/api/products?search=${encodeURIComponent(search)}`
        : '/api/products';

      const response = await fetch(url);
      const data = await response.json();

      if (data.products) {
        setAvailableProducts(data.products);
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des produits:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Charger les produits au montage du composant
  useEffect(() => {
    if (modalActive) {
      fetchProducts();
    }
  }, [modalActive, fetchProducts]);

  // Recherche avec debounce
  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    if (modalActive) {
      const timeout = setTimeout(() => {
        fetchProducts(searchQuery);
      }, 300); // Attendre 300ms après la dernière frappe

      setSearchTimeout(timeout);
    }

    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchQuery, modalActive, fetchProducts]);

  const handleProductToggle = useCallback((product: Product) => {
    const isSelected = selectedProducts.some(p => p.id === product.id);

    if (isSelected) {
      onProductsChange(selectedProducts.filter(p => p.id !== product.id));
    } else {
      onProductsChange([...selectedProducts, product]);
    }
  }, [selectedProducts, onProductsChange]);

  const handlePointsCostChange = useCallback((productId: string, newCost: number) => {
    const updatedProducts = selectedProducts.map(product =>
      product.id === productId ? { ...product, pointsCost: newCost } : product
    );
    onProductsChange(updatedProducts);
  }, [selectedProducts, onProductsChange]);

  const removeProduct = useCallback((productId: string) => {
    onProductsChange(selectedProducts.filter(p => p.id !== productId));
  }, [selectedProducts, onProductsChange]);

  return (
    <Box>
      <BlockStack gap="400">
        <InlineStack align="space-between">
          <Text as="h3" variant="headingMd">
            Produits échangeables avec des points
          </Text>
          <Button onClick={() => setModalActive(true)}>
            Ajouter des produits
          </Button>
        </InlineStack>

        <Text as="p" variant="bodySm" tone="subdued">
          Sélectionnez les produits que vos clients peuvent acheter avec leurs points de fidélité.
        </Text>

        {selectedProducts.length === 0 ? (
          <Card>
            <Box padding="400">
              <Text as="p" variant="bodyMd" alignment="center" tone="subdued">
                Aucun produit sélectionné. Cliquez sur "Ajouter des produits" pour commencer.
              </Text>
            </Box>
          </Card>
        ) : (
          <BlockStack gap="300">
            {selectedProducts.map((product) => (
              <Card key={product.id}>
                <Box padding="400">
                  <InlineStack gap="400" align="space-between">
                    <InlineStack gap="300" align="center">
                      <Thumbnail
                        source={product.image || 'https://via.placeholder.com/50'}
                        alt={product.title}
                        size="small"
                      />
                      <BlockStack gap="100">
                        <Text as="h4" variant="headingSm">{product.title}</Text>
                        <Text as="p" variant="bodySm" tone="subdued">Prix: {product.price}</Text>
                      </BlockStack>
                    </InlineStack>

                    <InlineStack gap="200" align="center">
                      <TextField
                        label=""
                        type="number"
                        value={product.pointsCost.toString()}
                        onChange={(value) => handlePointsCostChange(product.id, parseInt(value) || 0)}
                        suffix="points"
                        autoComplete="off"
                        min={0}
                      />
                      <Button
                        variant="plain"
                        tone="critical"
                        onClick={() => removeProduct(product.id)}
                      >
                        Supprimer
                      </Button>
                    </InlineStack>
                  </InlineStack>
                </Box>
              </Card>
            ))}
          </BlockStack>
        )}
      </BlockStack>

      <Modal
        open={modalActive}
        onClose={() => setModalActive(false)}
        title="Sélectionner des produits"
        primaryAction={{
          content: 'Fermer',
          onAction: () => setModalActive(false),
        }}
      >
        <Modal.Section>
          <BlockStack gap="400">
            <TextField
              label="Rechercher des produits"
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder="Nom du produit..."
              autoComplete="off"
            />

            {loading ? (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Spinner size="large" />
                <Text as="p" variant="bodyMd">Chargement des produits...</Text>
              </div>
            ) : (
              <ResourceList
                resourceName={{ singular: 'produit', plural: 'produits' }}
                items={availableProducts}
                renderItem={(item) => {
                  const product = item as unknown as Product;
                  const isSelected = selectedProducts.some(p => p.id === product.id);

                  return (
                    <ResourceItem
                      id={product.id}
                      onClick={() => handleProductToggle(product)}
                    >
                      <InlineStack gap="300" align="center">
                        <Checkbox
                          label=""
                          checked={isSelected}
                          onChange={() => handleProductToggle(product)}
                        />
                        <Thumbnail
                          source={product.image || 'https://via.placeholder.com/50'}
                          alt={product.title}
                          size="small"
                        />
                        <BlockStack gap="100">
                          <Text as="h4" variant="headingSm">{product.title}</Text>
                          <Text as="p" variant="bodySm" tone="subdued">
                            {product.price} • {product.pointsCost} points
                          </Text>
                        </BlockStack>
                        {isSelected && (
                          <Badge tone="success">Sélectionné</Badge>
                        )}
                      </InlineStack>
                    </ResourceItem>
                  );
                }}
              />
            )}
          </BlockStack>
        </Modal.Section>
      </Modal>
    </Box>
  );
}
