import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import { useState, useCallback } from "react";
import { authenticate } from "../shopify.server";
import { getReferralSettings, upsertReferralSettings, getDefaultReferralSettings } from "../models/ReferralSettings.server";
import { getProgramStats } from "../models/ProgramStats.server";
import {
  Layout,
  Card,
  FormLayout,
  TextField,
  Button,
  BlockStack,
  Text,
  Banner,
  Box,
  InlineStack,
  Icon,
  ChoiceList,
  Select,
  Badge,
  List,
  Toast,
  Frame,
} from "@shopify/polaris";
import { GiftCardIcon } from "@shopify/polaris-icons";
import { AdminLayout } from "app/components/Layout/AdminLayout";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // Récupérer les paramètres de parrainage existants
    let settings = await getReferralSettings(shop);

    // Si aucun paramètre n'existe, utiliser les valeurs par défaut
    if (!settings) {
      settings = getDefaultReferralSettings();
    }

    // Récupérer les statistiques du programme
    const stats = await getProgramStats(shop);

    return json({ settings, stats });
  } catch (error) {
    console.error("Error loading referral settings:", error);
    // En cas d'erreur, retourner les paramètres par défaut
    return json({ settings: getDefaultReferralSettings() });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  const formData = await request.formData();
  const actionType = formData.get("actionType") as string;

  try {
    if (actionType === "toggle") {
      // Basculer l'état actif/inactif
      const currentSettings = await getReferralSettings(shop) || getDefaultReferralSettings();
      const updatedSettings = {
        ...currentSettings,
        active: !currentSettings.active
      };

      await upsertReferralSettings(shop, updatedSettings);
      return json({ success: true, message: `Programme ${updatedSettings.active ? 'activé' : 'désactivé'}` });
    }

    if (actionType === "save") {
      // Sauvegarder tous les paramètres
      const settingsData = {
        active: formData.get("active") === "true",
        referrerReward: {
          type: formData.get("referrerRewardType") as "points" | "discount" | "fixed",
          amount: parseInt(formData.get("referrerRewardAmount") as string) || 0
        },
        referredReward: {
          type: formData.get("referredRewardType") as "points" | "discount" | "fixed",
          amount: parseInt(formData.get("referredRewardAmount") as string) || 0
        },
        minimumPurchase: parseInt(formData.get("minimumPurchase") as string) || 0,
        expiryDays: parseInt(formData.get("expiryDays") as string) || 30,
        customMessage: formData.get("customMessage") as string || ""
      };

      await upsertReferralSettings(shop, settingsData);
      return json({ success: true, message: "Paramètres sauvegardés avec succès" });
    }

    return json({ error: "Action non reconnue" }, { status: 400 });
  } catch (error) {
    console.error("Error saving referral settings:", error);
    return json({ error: "Erreur lors de la sauvegarde" }, { status: 500 });
  }
};

export default function ProgramReferrals() {
  const loaderData = useLoaderData<typeof loader>();
  const initialSettings = loaderData.settings;
  const stats = (loaderData as any).stats;
  const submit = useSubmit();

  // État local pour les formulaires
  const [settings, setSettings] = useState(initialSettings);
  const [hasChanges, setHasChanges] = useState(false);
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const showToast = (message: string) => {
    setToastMessage(message);
    setToastActive(true);
  };

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    formData.append("actionType", "save");
    formData.append("active", settings.active.toString());
    formData.append("referrerRewardType", settings.referrerReward.type);
    formData.append("referrerRewardAmount", settings.referrerReward.amount.toString());
    formData.append("referredRewardType", settings.referredReward.type);
    formData.append("referredRewardAmount", settings.referredReward.amount.toString());
    formData.append("minimumPurchase", settings.minimumPurchase.toString());
    formData.append("expiryDays", settings.expiryDays.toString());
    formData.append("customMessage", settings.customMessage);

    submit(formData, {
      method: "post",
      preventScrollReset: true
    });

    setHasChanges(false);
    showToast("Paramètres sauvegardés avec succès !");
  };

  const handleToggleActive = () => {
    const formData = new FormData();
    formData.append("actionType", "toggle");

    submit(formData, {
      method: "post",
      preventScrollReset: true
    });

    // Mettre à jour l'état local immédiatement
    setSettings(prev => ({ ...prev, active: !prev.active }));
    showToast(settings.active ? "Programme désactivé" : "Programme activé");
  };

  // Handlers pour mettre à jour l'état
  const updateSetting = useCallback((field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  }, []);

  const updateNestedSetting = useCallback((parent: keyof typeof settings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [parent]: {
        ...(prev[parent] as any),
        [field]: value
      }
    }));
    setHasChanges(true);
  }, [settings]);

  const rewardTypes = [
    { label: "Points", value: "points" },
    { label: "Réduction fixe (€)", value: "fixed" },
    { label: "Pourcentage (%)", value: "discount" },
  ];

  // Fonctions pour les textes dynamiques
  const getRewardLabel = (type: string) => {
    switch (type) {
      case "points": return "Montant en points";
      case "fixed": return "Montant de la réduction (€)";
      case "discount": return "Pourcentage de réduction (%)";
      default: return "Montant de la récompense";
    }
  };

  const getRewardText = (amount: number, type: string) => {
    switch (type) {
      case "points": return `${amount} points`;
      case "fixed": return `${amount}€ de réduction`;
      case "discount": return `${amount}% de réduction`;
      default: return `${amount}`;
    }
  };

  const toastMarkup = toastActive ? (
    <Toast
      content={toastMessage}
      onDismiss={() => setToastActive(false)}
      duration={4000}
    />
  ) : null;

  return (
    <Frame>
      <AdminLayout title="Programme de parrainage">
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            {/* Description du programme de parrainage */}
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Programme de parrainage</Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  Le programme de parrainage permet à vos clients fidèles de recommander votre boutique à leurs amis et famille.
                  Quand un client parraine un ami qui effectue son premier achat, les deux parties reçoivent des récompenses.
                  C'est un excellent moyen d'acquérir de nouveaux clients tout en récompensant la fidélité de vos clients existants.
                </Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  <strong>Comment ça fonctionne :</strong>
                </Text>
                <List type="number">
                  <List.Item>Le parrain partage son code de parrainage unique avec ses amis</List.Item>
                  <List.Item>Le filleul utilise ce code lors de sa première commande</List.Item>
                  <List.Item>Si la commande atteint le montant minimum, les récompenses sont distribuées</List.Item>
                  <List.Item>Le parrain et le filleul reçoivent leurs récompenses respectives</List.Item>
                </List>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                  <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                    <Text as="h2" variant="headingMd">État du programme</Text>
                    <Badge tone={settings.active ? "success" : "attention"}>
                      {settings.active ? "Actif" : "Inactif"}
                    </Badge>
                  </div>
                  <Button
                    tone={settings.active ? "critical" : "success"}
                    onClick={handleToggleActive}
                  >
                    {settings.active ? "Désactiver" : "Activer"}
                  </Button>
                </div>
                <Text as="p" variant="bodyMd" tone="subdued">
                  {settings.active
                    ? "Vos clients peuvent actuellement parrainer leurs amis et gagner des récompenses."
                    : "Le programme de parrainage est actuellement désactivé. Activez-le pour permettre à vos clients de parrainer leurs amis."
                  }
                </Text>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Récompense du parrain</Text>
                <Box paddingBlockEnd="400">
                  <InlineStack gap="200" align="center">
                    <Icon source={GiftCardIcon} />
                    <Text as="span" variant="bodyMd">
                      Le parrain reçoit {getRewardText(settings.referrerReward.amount, settings.referrerReward.type)}
                    </Text>
                  </InlineStack>
                </Box>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <Select
                      label="Type de récompense"
                      options={rewardTypes}
                      name="referrerRewardType"
                      value={settings.referrerReward.type}
                      onChange={(value) => updateNestedSetting('referrerReward', 'type', value)}
                    />
                    <TextField
                      label={getRewardLabel(settings.referrerReward.type)}
                      type="number"
                      name="referrerRewardAmount"
                      value={settings.referrerReward.amount.toString()}
                      onChange={(value) => updateNestedSetting('referrerReward', 'amount', parseInt(value) || 0)}
                      autoComplete="off"
                      min={0}
                      max={settings.referrerReward.type === "discount" ? 100 : undefined}
                    />
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Récompense du filleul</Text>
                <Box paddingBlockEnd="400">
                  <InlineStack gap="200" align="center">
                    <Icon source={GiftCardIcon} />
                    <Text as="span" variant="bodyMd">
                      Le filleul reçoit {getRewardText(settings.referredReward.amount, settings.referredReward.type)}
                    </Text>
                  </InlineStack>
                </Box>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <Select
                      label="Type de récompense"
                      options={rewardTypes}
                      name="referredRewardType"
                      value={settings.referredReward.type}
                      onChange={(value) => updateNestedSetting('referredReward', 'type', value)}
                    />
                    <TextField
                      label={getRewardLabel(settings.referredReward.type)}
                      type="number"
                      name="referredRewardAmount"
                      value={settings.referredReward.amount.toString()}
                      onChange={(value) => updateNestedSetting('referredReward', 'amount', parseInt(value) || 0)}
                      autoComplete="off"
                      min={0}
                      max={settings.referredReward.type === "discount" ? 100 : undefined}
                    />
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Conditions</Text>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <TextField
                      label="Montant minimum d'achat pour le filleul (€)"
                      type="number"
                      name="minimumPurchase"
                      value={settings.minimumPurchase.toString()}
                      onChange={(value) => updateSetting('minimumPurchase', parseInt(value) || 0)}
                      autoComplete="off"
                      helpText="Montant minimum que le filleul doit dépenser pour valider le parrainage"
                      min={0}
                    />
                    <TextField
                      label="Durée de validité de l'invitation (jours)"
                      type="number"
                      name="expiryDays"
                      value={settings.expiryDays.toString()}
                      onChange={(value) => updateSetting('expiryDays', parseInt(value) || 0)}
                      autoComplete="off"
                      helpText="Nombre de jours pendant lesquels l'invitation reste valide"
                      min={0}
                    />
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Personnalisation</Text>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <TextField
                      label="Message d'invitation"
                      name="customMessage"
                      value={settings.customMessage}
                      onChange={(value) => updateSetting('customMessage', value)}
                      autoComplete="off"
                      multiline={3}
                      helpText="Ce message sera affiché sur la page de parrainage"
                    />
                    <Button submit variant="primary">Enregistrer les modifications</Button>
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>

        <Layout.Section variant="oneThird">
          <BlockStack gap="500">
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Statistiques</Text>
                <BlockStack gap="200">
                  <Text as="p" variant="bodyMd">
                    Parrainages en attente : <Text as="span" variant="headingSm">{stats?.pendingReferrals || 0}</Text>
                  </Text>
                  <Text as="p" variant="bodyMd">
                    Parrainages complétés : <Text as="span" variant="headingSm">{stats?.completedReferrals || 0}</Text>
                  </Text>
                  <Text as="p" variant="bodyMd">
                    Points distribués : <Text as="span" variant="headingSm">{stats?.pointsDistributed || 0}</Text>
                  </Text>
                </BlockStack>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Aide</Text>
                <Text as="p" variant="bodyMd">
                  Le programme de parrainage permet à vos clients de recommander votre boutique à leurs amis.
                  Les parrains et les filleuls reçoivent des récompenses lorsque le parrainage est validé.
                </Text>
                <Text as="p" variant="bodyMd">
                  Un parrainage est validé lorsque le filleul effectue son premier achat atteignant le montant minimum défini.
                </Text>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
      {toastMarkup}
    </AdminLayout>
    </Frame>
  );
}
