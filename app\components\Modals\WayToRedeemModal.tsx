import { useState, useCallback, useEffect } from "react";
import {
  Modal,
  FormLayout,
  TextField,
  Button,
  Select,
  Checkbox,
  BlockStack,
  Text
} from "@shopify/polaris";

interface WayToRedeemModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: WayToRedeemFormData) => void;
  wayToRedeem?: any;
  isLoading?: boolean;
}

export interface WayToRedeemFormData {
  name: string;
  description: string;
  redeemType: "discount" | "product" | "shipping";
  redeemValue: string;
  pointsCost: string;
  icon: string;
  isActive: boolean;
}

export function WayToRedeemModal({ isOpen, onClose, onSave, wayToRedeem, isLoading = false }: WayToRedeemModalProps) {
  const [formState, setFormState] = useState<WayToRedeemFormData>({
    name: wayToRedeem?.name || "",
    description: wayToRedeem?.description || "",
    redeemType: wayToRedeem?.redeemType || "discount",
    redeemValue: wayToRedeem?.redeemValue?.toString() || "",
    pointsCost: wayToRedeem?.pointsCost?.toString() || "",
    icon: wayToRedeem?.icon || "discount",
    isActive: wayToRedeem?.isActive ?? true
  });

  const [errors, setErrors] = useState<Partial<WayToRedeemFormData>>({});

  // Mettre à jour le formulaire quand wayToRedeem change
  useEffect(() => {
    if (wayToRedeem) {
      setFormState({
        name: wayToRedeem.name || "",
        description: wayToRedeem.description || "",
        redeemType: wayToRedeem.redeemType || "discount",
        redeemValue: wayToRedeem.redeemValue?.toString() || "",
        pointsCost: wayToRedeem.pointsCost?.toString() || "",
        icon: wayToRedeem.icon || "discount",
        isActive: wayToRedeem.isActive ?? true
      });
    } else {
      setFormState({
        name: "",
        description: "",
        redeemType: "discount",
        redeemValue: "",
        pointsCost: "",
        icon: "discount",
        isActive: true
      });
    }
    setErrors({});
  }, [wayToRedeem, isOpen]);

  const handleSubmit = useCallback(() => {
    // Validation
    const newErrors: Partial<WayToRedeemFormData> = {};

    if (!formState.name.trim()) {
      newErrors.name = "Le nom est requis";
    }

    if (!formState.description.trim()) {
      newErrors.description = "La description est requise";
    }

    if (!formState.redeemValue || parseFloat(formState.redeemValue) <= 0) {
      newErrors.redeemValue = "La valeur doit être positive";
    }

    if (!formState.pointsCost || parseInt(formState.pointsCost) <= 0) {
      newErrors.pointsCost = "Le coût en points doit être positif";
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      onSave(formState);
    }
  }, [formState, onSave]);

  const handleClose = useCallback(() => {
    setFormState({
      name: "",
      description: "",
      redeemType: "discount",
      redeemValue: "",
      pointsCost: "",
      icon: "discount",
      isActive: true
    });
    setErrors({});
    onClose();
  }, [onClose]);

  const iconOptions = [
    { label: "Réduction", value: "discount" },
    { label: "Produit gratuit", value: "product" },
    { label: "Livraison gratuite", value: "shipping" },
    { label: "Cadeau", value: "gift" },
    { label: "Bon d'achat", value: "voucher" }
  ];

  const redeemTypeOptions = [
    { label: "Réduction sur commande", value: "discount" },
    { label: "Produit gratuit", value: "product" },
    { label: "Livraison gratuite", value: "shipping" }
  ];

  const getRedeemValueLabel = () => {
    switch (formState.redeemType) {
      case "discount":
        return "Montant de la réduction (€)";
      case "product":
        return "Valeur du produit (€)";
      case "shipping":
        return "Valeur de la livraison (€)";
      default:
        return "Valeur (€)";
    }
  };

  return (
    <Modal
      open={isOpen}
      onClose={handleClose}
      title={wayToRedeem ? "Modifier la façon d'échanger des points" : "Ajouter une façon d'échanger des points"}
      primaryAction={{
        content: wayToRedeem ? "Enregistrer" : "Créer",
        onAction: handleSubmit,
        loading: isLoading
      }}
      secondaryActions={[
        {
          content: "Annuler",
          onAction: handleClose
        }
      ]}
    >
      <Modal.Section>
        <FormLayout>
          <TextField
            label="Nom"
            value={formState.name}
            onChange={(value) => setFormState(prev => ({ ...prev, name: value }))}
            placeholder="ex: Réduction sur commande"
            error={errors.name}
            autoComplete="off"
            requiredIndicator
          />

          <TextField
            label="Description"
            value={formState.description}
            onChange={(value) => setFormState(prev => ({ ...prev, description: value }))}
            placeholder="ex: Obtenez une réduction sur vos commandes"
            error={errors.description}
            autoComplete="off"
            requiredIndicator
          />

          <Select
            label="Type d'échange"
            options={redeemTypeOptions}
            value={formState.redeemType}
            onChange={(value) => setFormState(prev => ({ ...prev, redeemType: value as "discount" | "product" | "shipping" }))}
          />

          <TextField
            label={getRedeemValueLabel()}
            value={formState.redeemValue}
            onChange={(value) => setFormState(prev => ({ ...prev, redeemValue: value }))}
            type="number"
            step="0.01"
            min="0"
            error={errors.redeemValue}
            autoComplete="off"
            requiredIndicator
          />

          <TextField
            label="Coût en points"
            value={formState.pointsCost}
            onChange={(value) => setFormState(prev => ({ ...prev, pointsCost: value }))}
            type="number"
            min="1"
            error={errors.pointsCost}
            autoComplete="off"
            requiredIndicator
          />

          <Select
            label="Icône"
            options={iconOptions}
            value={formState.icon}
            onChange={(value) => setFormState(prev => ({ ...prev, icon: value }))}
          />

          <Checkbox
            label="Actif"
            checked={formState.isActive}
            onChange={(checked) => setFormState(prev => ({ ...prev, isActive: checked }))}
          />
        </FormLayout>
      </Modal.Section>
    </Modal>
  );
}
