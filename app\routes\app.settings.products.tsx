import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import { authenticate } from "../shopify.server";
import { getExchangeableProducts, addExchangeableProduct, removeExchangeableProduct, updateProductPointsCost, updateProductStatus, calculatePointsCost } from "../models/ExchangeableProducts.server";
import {
  Card,
  Layout,
  Page,
  BlockStack,
  InlineStack,
  Text,
  Button,
  DataTable,
  Badge,
  EmptyState,
  Modal,
  Toast,
  Frame,
} from "@shopify/polaris";
import { useState, useCallback } from "react";

import { ProductSelector } from "../components/ProductSelector";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const { shop } = session;

  try {
    const exchangeableProducts = await getExchangeableProducts(shop);

    // Recalculer les points pour chaque produit selon les paramètres actuels du programme
    const productsWithUpdatedPoints = await Promise.all(
      exchangeableProducts.map(async (product) => {
        const calculatedPointsCost = await calculatePointsCost(shop, product.price || '0');
        return {
          ...product,
          pointsCost: calculatedPointsCost
        };
      })
    );

    return json({ exchangeableProducts: productsWithUpdatedPoints });
  } catch (error) {
    console.error("Error loading exchangeable products:", error);
    return json({ exchangeableProducts: [] });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const { shop } = session;

  try {
    const formData = await request.formData();
    const action = formData.get('action');

    if (action === 'create') {
      const productsJson = formData.get('products') as string;

      if (!productsJson) {
        return json({ error: "Aucun produit sélectionné" }, { status: 400 });
      }

      try {
        const selectedProducts = JSON.parse(productsJson);
        let successCount = 0;
        let errorCount = 0;

        // Traiter chaque produit sélectionné
        for (const product of selectedProducts) {
          // Calculer le coût en points basé sur le prix et les paramètres du programme
          const calculatedPointsCost = await calculatePointsCost(shop, product.price || '0');

          const productData = {
            shopifyProductId: product.id,
            title: product.title,
            handle: product.handle || product.id,
            image: product.image || '',
            price: product.price || '0',
            pointsCost: calculatedPointsCost,
            active: true
          };

          const success = await addExchangeableProduct(shop, productData);
          if (success) {
            successCount++;
          } else {
            errorCount++;
          }
        }

        if (successCount > 0) {
          const message = errorCount > 0
            ? `${successCount} produit(s) ajouté(s) avec succès, ${errorCount} déjà existant(s)`
            : `${successCount} produit(s) ajouté(s) avec succès`;
          return json({ success: true, message });
        } else {
          return json({ error: "Aucun produit n'a pu être ajouté (déjà existants)" }, { status: 400 });
        }
      } catch (parseError) {
        return json({ error: "Erreur lors du traitement des produits" }, { status: 400 });
      }
    }

    if (action === 'delete') {
      const id = formData.get('id') as string;
      const success = await removeExchangeableProduct(shop, id);
      if (success) {
        return json({ success: true, message: "Produit échangeable supprimé avec succès" });
      } else {
        return json({ error: "Erreur lors de la suppression" }, { status: 500 });
      }
    }

    if (action === 'updatePoints') {
      const id = formData.get('id') as string;
      const pointsCost = parseInt(formData.get('pointsCost') as string);
      const success = await updateProductPointsCost(shop, id, pointsCost);
      if (success) {
        return json({ success: true, message: "Coût en points mis à jour avec succès" });
      } else {
        return json({ error: "Erreur lors de la mise à jour" }, { status: 500 });
      }
    }

    if (action === 'toggleStatus') {
      const id = formData.get('id') as string;
      const currentStatus = formData.get('currentStatus') === 'true';
      const success = await updateProductStatus(shop, id, !currentStatus);
      if (success) {
        return json({ success: true, message: `Produit ${!currentStatus ? 'activé' : 'désactivé'} avec succès` });
      } else {
        return json({ error: "Erreur lors de la mise à jour du statut" }, { status: 500 });
      }
    }

    return json({ error: "Action non reconnue" }, { status: 400 });
  } catch (error) {
    console.error("Error managing exchangeable products:", error);
    return json({ error: "Erreur lors de l'opération" }, { status: 500 });
  }
};

export default function ExchangeableProducts() {
  const { exchangeableProducts } = useLoaderData<typeof loader>();
  const submit = useSubmit();

  // États pour le modal d'ajout
  const [modalActive, setModalActive] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);

  // État pour les toasts
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastError, setToastError] = useState(false);

  const handleModalToggle = useCallback(() => {
    setModalActive(!modalActive);
    if (!modalActive) {
      // Reset form when opening modal
      setSelectedProducts([]);
    }
  }, [modalActive]);

  const handleSubmit = useCallback(() => {
    if (selectedProducts.length === 0) {
      setToastMessage('Veuillez sélectionner au moins un produit');
      setToastError(true);
      setToastActive(true);
      return;
    }

    // Envoyer tous les produits sélectionnés en une seule requête
    const formData = new FormData();
    formData.append('action', 'create');
    formData.append('products', JSON.stringify(selectedProducts));

    submit(formData, { method: 'post' });
    handleModalToggle();

    setToastMessage(`Ajout de ${selectedProducts.length} produit(s) en cours...`);
    setToastError(false);
    setToastActive(true);
  }, [selectedProducts, submit, handleModalToggle]);

  const handleDelete = useCallback((id: string) => {
    const formData = new FormData();
    formData.append('action', 'delete');
    formData.append('id', id);
    submit(formData, { method: 'post' });

    setToastMessage('Produit échangeable supprimé avec succès');
    setToastError(false);
    setToastActive(true);
  }, [submit]);

  const handleToggleStatus = useCallback((id: string, currentStatus: boolean) => {
    const formData = new FormData();
    formData.append('action', 'toggleStatus');
    formData.append('id', id);
    formData.append('currentStatus', currentStatus.toString());
    submit(formData, { method: 'post' });

    setToastMessage(`Statut du produit ${!currentStatus ? 'activé' : 'désactivé'} avec succès`);
    setToastError(false);
    setToastActive(true);
  }, [submit]);



  const toggleToastActive = useCallback(() => setToastActive((active) => !active), []);

  const toastMarkup = toastActive ? (
    <Toast
      content={toastMessage}
      onDismiss={toggleToastActive}
      error={toastError}
    />
  ) : null;

  // Préparer les données pour le tableau
  const rows = exchangeableProducts.map((product: any) => [
    product.image ? (
      <img src={product.image} alt={product.title} style={{ width: '40px', height: '40px', objectFit: 'cover', borderRadius: '4px' }} />
    ) : (
      <div style={{ width: '40px', height: '40px', background: '#f0f0f0', borderRadius: '4px' }} />
    ),
    <div>
      <Text as="p" variant="bodyMd" fontWeight="semibold">{product.title}</Text>
      <Text as="p" variant="bodySm" tone="subdued">Prix: {product.price}€</Text>
    </div>,
    <div>
      <Text as="p" variant="bodyMd" fontWeight="semibold" tone="success">
        {product.pointsCost} points
      </Text>
      <Text as="p" variant="bodySm" tone="subdued">
        Calculé automatiquement
      </Text>
    </div>,
    <Badge tone={product.active ? 'success' : 'critical'}>
      {product.active ? 'Actif' : 'Inactif'}
    </Badge>,
    <InlineStack gap="200">
      <Button
        size="micro"
        variant={product.active ? 'secondary' : 'primary'}
        onClick={() => handleToggleStatus(product.id, product.active)}
      >
        {product.active ? 'Désactiver' : 'Activer'}
      </Button>
      <Button
        size="micro"
        tone="critical"
        onClick={() => handleDelete(product.id)}
      >
        Supprimer
      </Button>
    </InlineStack>
  ]);

  const emptyStateMarkup = exchangeableProducts.length === 0 ? (
    <EmptyState
      heading="Aucun produit échangeable configuré"
      action={{
        content: 'Ajouter un produit',
        onAction: handleModalToggle,
      }}
      image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
    >
      <p>Commencez par ajouter des produits que vos clients peuvent échanger contre des points.</p>
    </EmptyState>
  ) : null;

  return (
    <Frame>
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            <Card>
              <BlockStack gap="400">
                <InlineStack align="space-between">
                  <div>
                    <Text as="h2" variant="headingMd">
                      Produits échangeables
                    </Text>
                    <Text as="p" variant="bodySm" tone="subdued">
                      Gérez les produits que vos clients peuvent obtenir en échange de points
                    </Text>
                  </div>
                  <Button variant="primary" onClick={handleModalToggle}>
                    Ajouter un produit
                  </Button>
                </InlineStack>
              </BlockStack>
            </Card>

            <Card>
              {emptyStateMarkup || (
                <DataTable
                  columnContentTypes={['text', 'text', 'text', 'text', 'text']}
                  headings={['Image', 'Produit', 'Coût en points', 'Statut', 'Actions']}
                  rows={rows}
                />
              )}
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>

      <Modal
        open={modalActive}
        onClose={handleModalToggle}
        title="Ajouter un produit échangeable"
        primaryAction={{
          content: 'Ajouter',
          onAction: handleSubmit,
        }}
        secondaryActions={[
          {
            content: 'Annuler',
            onAction: handleModalToggle,
          },
        ]}
      >
        <Modal.Section>
          <BlockStack gap="400">
            <Text as="p" variant="bodyMd">
              Sélectionnez les produits que vos clients pourront échanger contre des points.
              Le coût en points sera défini dans les paramètres du programme.
            </Text>

            <ProductSelector
              selectedProducts={selectedProducts}
              onProductsChange={setSelectedProducts}
            />

            {selectedProducts.length > 0 && (
              <div style={{
                padding: '12px',
                background: '#f0f8ff',
                borderRadius: '8px',
                border: '1px solid #e1e8ff'
              }}>
                <Text as="p" variant="bodySm" tone="subdued">
                  {selectedProducts.length} produit(s) sélectionné(s). Le coût en points sera configuré automatiquement selon les paramètres du programme.
                </Text>
              </div>
            )}
          </BlockStack>
        </Modal.Section>
      </Modal>

      {toastMarkup}
    </Frame>
  );
}
