import {
  Frame,
  Navigation,
  Page,
  TopBar,
  Text,
} from "@shopify/polaris";
import {
  HomeIcon,
  SettingsIcon,
  CursorIcon,
  GiftCardIcon,
  MarketsIcon,
  OrderIcon,
  EmailIcon,
  StarIcon,
} from "@shopify/polaris-icons";
import { useNavigate, useLocation, Link } from "@remix-run/react";
import { useState, useCallback } from "react";

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}

export function AdminLayout({ children, title = "Administration" }: AdminLayoutProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const [userMenuActive, setUserMenuActive] = useState(false);
  const [searchActive, setSearchActive] = useState(false);

  const toggleUserMenuActive = useCallback(
    () => setUserMenuActive((userMenuActive) => !userMenuActive),
    [],
  );

  const toggleSearchActive = useCallback(
    () => setSearchActive((searchActive) => !searchActive),
    [],
  );

  const userMenuActions = [
    {
      items: [{ content: "Déconnexion", onAction: () => console.log("logout") }],
    },
  ];

  const navigationItems = [
    {
      label: "Tableau de bord",
      icon: HomeIcon,
      url: "/app/admin",
      selected: location.pathname === "/app/admin",
    },
    {
      label: "Programme",
      icon: StarIcon,
      url: "/app/program",
      selected: location.pathname.startsWith("/app/program"),
      subNavigationItems: [
        {
          label: "Vue d'ensemble",
          url: "/app/program",
          selected: location.pathname === "/app/program",
        },
        {
          label: "Configuration des points",
          url: "/app/program/points",
          selected: location.pathname === "/app/program/points",
        },
        {
          label: "Parrainage",
          url: "/app/program/referrals",
          selected: location.pathname === "/app/program/referrals",
        },
        {
          label: "Programme VIP",
          url: "/app/program/vip",
          selected: location.pathname === "/app/program/vip",
        },
        {
          label: "Campagnes bonus",
          url: "/app/program/campaigns",
          selected: location.pathname === "/app/program/campaigns",
        },
      ],
    },
    {
      label: "Promotions",
      icon: MarketsIcon,
      url: "/app/promotions",
      selected: location.pathname === "/app/promotions",
    },
    {
      label: "Historique",
      icon: OrderIcon,
      url: "/app/history",
      selected: location.pathname === "/app/history",
    },
    {
      label: "Boutique de points",
      icon: GiftCardIcon,
      url: "/app/points-shop",
      selected: location.pathname === "/app/points-shop",
    },
  ];

  const secondaryNavItems = [
    {
      label: "Paramètres",
      icon: SettingsIcon,
      url: "/app/settings",
      selected: location.pathname.startsWith("/app/settings"),
      subNavigationItems: [
        {
          label: "Paramètres généraux",
          url: "/app/settings",
          selected: location.pathname === "/app/settings",
        },
        {
          label: "Personnaliser le widget",
          url: "/app/settings/widget",
          selected: location.pathname === "/app/settings/widget",
        },
        {
          label: "Produits échangeables",
          url: "/app/settings/products",
          selected: location.pathname === "/app/settings/products",
        },
      ],
    },
  ];

  const searchField = (
    <TopBar.SearchField
      onChange={() => {}}
      value=""
      placeholder="Rechercher..."
    />
  );

  return (
    <Frame
      navigation={
        <Navigation location={location.pathname}>
          <Navigation.Section
            items={navigationItems}
          />
          <Navigation.Section
            items={secondaryNavItems}
            separator
          />
        </Navigation>
      }
      topBar={
        <TopBar
          showNavigationToggle
          userMenu={
            <TopBar.UserMenu
              actions={userMenuActions}
              name="Admin"
              initials="A"
              open={userMenuActive}
              onToggle={toggleUserMenuActive}
            />
          }
          searchField={searchField}
          searchResultsVisible={searchActive}
          onSearchResultsDismiss={() => setSearchActive(false)}
        />
      }
    >
      {/* narrowWidth || fullWidth */}
      <Page title={title} >
        {children}
      </Page>
    </Frame>
  );
}
