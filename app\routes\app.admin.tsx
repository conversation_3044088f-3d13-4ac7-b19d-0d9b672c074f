import { j<PERSON>, LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Grid,
  BlockStack,
  DataTable,
  Spinner,
} from "@shopify/polaris";
import { AdminLayout } from "../components/Layout/AdminLayout";
import db from "../db.server";
import { authenticate } from "../shopify.server";
import { getProgramStats } from "../models/ProgramStats.server";
import { lazy, Suspense } from "react";
import { useHydrated } from "../hooks/useHydrated";
import { Component } from "react";

// Charger dynamiquement les graphiques
const LineChart = lazy(() =>
  import("@shopify/polaris-viz").then((module) => ({ default: module.LineChart }))
);
const BarChart = lazy(() =>
  import("@shopify/polaris-viz").then((module) => ({ default: module.BarChart }))
);

// Composant ErrorBoundary pour capturer les erreurs dans les graphiques
class ErrorBoundary extends Component<{ children: React.ReactNode }> {
  state = { hasError: false };

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      return <div>Une erreur est survenue dans le graphique.</div>;
    }
    return this.props.children;
  }
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  // Récupérer les points totaux et le nombre de membres actifs
  const pointsStats = await db.customer.aggregate({
    where: {
      shop: shop,
    },
    _sum: {
      points: true,
    },
    _count: {
      customerId: true,
    },
  });

  // Calculer le taux d'utilisation (points utilisés / points gagnés) sur les 30 derniers jours
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  // Récupérer les données pour le graphique d'évolution sur 30 jours
  const dailyPoints = await db.pointsHistory.groupBy({
    by: ["timestamp"],
    where: {
      customer: {
        shop: shop,
      },
      timestamp: {
        gte: thirtyDaysAgo,
      },
    },
    _sum: {
      points: true,
    },
    orderBy: {
      timestamp: "asc",
    },
  });

  // Récupérer la distribution des points par type d'action
  const pointsByType = await db.pointsHistory.groupBy({
    by: ["action"],
    where: {
      customer: {
        shop: shop,
      },
      timestamp: {
        gte: thirtyDaysAgo,
      },
    },
    _sum: {
      points: true,
    },
  });

  const pointsActivity = await db.pointsHistory.groupBy({
    by: ["action"],
    where: {
      customer: {
        shop: shop,
      },
      timestamp: {
        gte: thirtyDaysAgo,
      },
    },
    _sum: {
      points: true,
    },
  });

  // Calculer le taux d'utilisation
  const pointsEarned = pointsActivity.find((p) => p.action === "earn")?._sum.points || 0;
  const pointsRedeemed = Math.abs(pointsActivity.find((p) => p.action === "redeem")?._sum.points || 0);
  const redemptionRate = pointsEarned > 0 ? Math.round((pointsRedeemed / pointsEarned) * 100) : 0;

  // Récupérer l'activité récente
  const recentActivity = await db.pointsHistory.findMany({
    where: {
      customer: {
        shop: shop,
      },
    },
    select: {
      customer: {
        select: {
          customerId: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
      action: true,
      points: true,
      timestamp: true,
    },
    orderBy: {
      timestamp: "desc",
    },
    take: 10,
  });

  // Formater l'activité récente pour le DataTable
  const formattedActivity = recentActivity.map((activity) => {
    const customerName = activity.customer.firstName && activity.customer.lastName
      ? `${activity.customer.firstName} ${activity.customer.lastName}`
      : activity.customer.email || `Client ${activity.customer.customerId}`;

    return [
      customerName,
      activity.action,
      `${activity.points > 0 ? "+" : ""}${activity.points} points`,
      new Date(activity.timestamp).toLocaleDateString("fr-FR"),
    ];
  });

  // Formater les données pour les graphiques
  const pointsEvolutionData = [
    {
      name: "Évolution des points",
      data: dailyPoints.map((day) => ({
        key: new Date(day.timestamp).toLocaleDateString("fr-FR"),
        value: day._sum.points || 0,
      })),
    },
  ];

  const pointsDistributionData = [
    {
      name: "Distribution des points",
      data: pointsByType.map((type) => ({
        key: type.action,
        value: Math.abs(type._sum.points || 0),
      })),
    },
  ];

  return json({
    stats: {
      totalPoints: pointsStats._sum.points || 0,
      activeMembers: pointsStats._count.customerId || 0,
      redemptionRate: `${redemptionRate}%`,
      recentActivity: formattedActivity,
      pointsEvolution: pointsEvolutionData,
      pointsDistribution: pointsDistributionData,
    },
  });
};

export default function Admin() {
  const { stats } = useLoaderData<typeof loader>();
  const isHydrated = useHydrated();

  const renderChart = (ChartComponent: any, data: any) => {
    if (!isHydrated) {
      return (
        <div style={{ height: "200px", display: "flex", alignItems: "center", justifyContent: "center" }}>
          <Spinner size="large" />
        </div>
      );
    }

    return (
      <ErrorBoundary>
        <Suspense
          fallback={
            <div style={{ height: "200px", display: "flex", alignItems: "center", justifyContent: "center" }}>
              <Spinner size="large" />
            </div>
          }
        >
          <ChartComponent data={data} theme="Light" />
        </Suspense>
      </ErrorBoundary>
    );
  };

  return (
    <AdminLayout title="Tableau de bord">
      <Layout>
        <Layout.Section>
          <Grid>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3 }}>
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Points totaux
                  </Text>
                  <Text as="p" variant="bodyLg" fontWeight="bold">
                    {stats.totalPoints.toLocaleString()}
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3 }}>
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Membres actifs
                  </Text>
                  <Text as="p" variant="bodyLg" fontWeight="bold">
                    {stats.activeMembers}
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3 }}>
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Taux d'utilisation
                  </Text>
                  <Text as="p" variant="bodyLg" fontWeight="bold">
                    {stats.redemptionRate}
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
          </Grid>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h3" variant="headingMd">
                Évolution des points (30 jours)
              </Text>
              {renderChart(LineChart, stats.pointsEvolution)}
            </BlockStack>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h3" variant="headingMd">
                Distribution des points par type
              </Text>
              {renderChart(BarChart, stats.pointsDistribution)}
            </BlockStack>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h3" variant="headingMd">
                Activité récente
              </Text>
              <DataTable
                columnContentTypes={["text", "text", "text", "text"]}
                headings={["Client", "Action", "Points", "Date"]}
                rows={stats.recentActivity}
              />
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
}

/* Alternative avec Chart.js (décommentez si @shopify/polaris-viz pose toujours problème) */
/*
import { Line, Bar } from "react-chartjs-2";
import { Chart as ChartJS, LineElement, BarElement, PointElement, LinearScale, CategoryScale, Tooltip, Legend } from "chart.js";

ChartJS.register(LineElement, BarElement, PointElement, LinearScale, CategoryScale, Tooltip, Legend);

const renderChart = (type: "line" | "bar", data: any) => {
  if (!isHydrated || !data?.[0]?.data?.length) {
    return (
      <div style={{ height: "200px", display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Spinner size="large" />
      </div>
    );
  }

  const chartData = {
    labels: data[0].data.map((item: any) => item.key),
    datasets: [
      {
        label: data[0].name,
        data: data[0].data.map((item: any) => item.value),
        borderColor: type === "line" ? "#2E7D32" : undefined,
        backgroundColor: type === "bar" ? "rgba(46, 125, 50, 0.6)" : "rgba(46, 125, 50, 0.2)",
        fill: type === "line",
      },
    ],
  };

  const ChartComponent = type === "line" ? Line : Bar;

  return (
    <ErrorBoundary>
      <ChartComponent data={chartData} />
    </ErrorBoundary>
  );
};

// Dans le JSX, remplacez les appels à renderChart :
{renderChart("line", stats.pointsEvolution)}
{renderChart("bar", stats.pointsDistribution)}
*/
