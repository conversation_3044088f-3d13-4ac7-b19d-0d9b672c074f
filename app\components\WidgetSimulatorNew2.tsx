interface WidgetSimulatorProps {
  primaryColor: string;
  secondaryColor: string;
  textColor: string;
  position: string;
  programName: string;
  programDescription: string;
  welcomeMessage?: string;
  customerData?: {
    name: string;
    email: string;
    points: number;
    orders: number;
    initials: string;
  };
  widgetSize?: string;
  widgetBorderRadius?: string;
  widgetShadow?: boolean;
  widgetAnimation?: boolean;
  showPointsOnButton?: boolean;
}

export function WidgetSimulator({
  primaryColor,
  secondaryColor,
  textColor,
  position,
  programName,
  programDescription,
  welcomeMessage,
  customerData,
  widgetSize = "medium",
  widgetBorderRadius = "rounded",
  widgetShadow = true,
  widgetAnimation = true,
  showPointsOnButton = true
}: WidgetSimulatorProps) {

  const defaultCustomer = {
    name: "<PERSON>",
    email: "<EMAIL>",
    points: 1234,
    orders: 5,
    initials: "J<PERSON>"
  };

  const customer = customerData || defaultCustomer;

  const getBorderRadius = () => {
    switch (widgetBorderRadius) {
      case 'square': return '8px';
      case 'pill': return '25px';
      default: return '16px';
    }
  };

  const getBoxShadow = () => {
    return widgetShadow ? '0 8px 32px rgba(0,0,0,0.15)' : '0 2px 8px rgba(0,0,0,0.1)';
  };

  return (
    <div style={{
      width: '100%',
      maxWidth: '400px',
      margin: '0 auto',
      background: 'var(--loyalty-widget-background, #ffffff)',
      borderRadius: getBorderRadius(),
      boxShadow: getBoxShadow(),
      overflow: 'hidden',
      border: '1px solid #e0e0e0',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      position: 'relative'
    }}>
      <style>{`
        :root {
          --loyalty-primary: ${primaryColor};
          --loyalty-secondary: ${secondaryColor};
          --loyalty-text-color: ${textColor};
          --loyalty-widget-background: #ffffff;
          --loyalty-surface: #f8f9fa;
          --loyalty-text: #212121;
          --loyalty-text-secondary: #757575;
          --loyalty-border: #e0e0e0;
          --loyalty-radius: 16px;
          --loyalty-radius-small: 8px;
          --loyalty-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
      `}</style>

      {/* Header du widget - exactement comme l'interface client */}
      <div style={{
        background: `linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%)`,
        color: 'var(--loyalty-text-color)',
        padding: '24px 20px',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Motifs décoratifs - comme dans loyalty-widget.css */}
        <div style={{
          position: 'absolute',
          top: '-50px',
          right: '-50px',
          width: '100px',
          height: '100px',
          borderRadius: '50%',
          background: 'rgba(255,255,255,0.1)',
          opacity: 0.5
        }} />
        <div style={{
          position: 'absolute',
          bottom: '-30px',
          left: '-30px',
          width: '60px',
          height: '60px',
          borderRadius: '50%',
          background: 'rgba(255,255,255,0.1)',
          opacity: 0.3
        }} />

        {/* Header content */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          position: 'relative',
          zIndex: 2
        }}>
          <div>
            <div style={{
              fontSize: '16px',
              fontWeight: '500',
              marginBottom: '4px',
              opacity: 0.9,
              color: 'var(--loyalty-text-color)'
            }}>
              Welcome to
            </div>
            <div style={{
              fontSize: '22px',
              fontWeight: 'bold',
              lineHeight: '1.2',
              color: 'var(--loyalty-text-color)'
            }}>
              {programName || 'Quickstart Points'}
            </div>
          </div>
          <button style={{
            background: 'rgba(255,255,255,0.2)',
            border: 'none',
            color: 'var(--loyalty-text-color)',
            cursor: 'pointer',
            fontSize: '16px',
            padding: '8px',
            borderRadius: '50%',
            width: '32px',
            height: '32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'var(--loyalty-transition)'
          }}>
            ×
          </button>
        </div>
      </div>

      {/* Contenu principal du widget */}
      <div style={{ padding: '24px 20px' }}>
        {/* Informations client - comme dans loyalty-widget.css */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '16px',
          padding: '16px',
          background: 'var(--loyalty-surface)',
          borderRadius: 'var(--loyalty-radius-small)',
          marginBottom: '20px'
        }}>
          {/* Avatar client */}
          <div style={{
            width: '48px',
            height: '48px',
            borderRadius: '50%',
            background: `linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%)`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: '600',
            fontSize: '18px'
          }}>
            {customerData?.initials || customerData?.name?.charAt(0)?.toUpperCase() || 'G'}
          </div>

          {/* Détails client */}
          <div style={{ flex: 1 }}>
            <div style={{
              fontSize: '16px',
              fontWeight: '600',
              marginBottom: '4px',
              color: 'var(--loyalty-text)'
            }}>
              {customerData?.name || 'Guest'}
            </div>
            <div style={{
              display: 'inline-block',
              padding: '4px 12px',
              borderRadius: '20px',
              fontSize: '12px',
              fontWeight: '600',
              textTransform: 'uppercase',
              letterSpacing: '0.5px',
              background: customerData?.name ? '#e8f5e8' : '#e3f2fd',
              color: customerData?.name ? 'var(--loyalty-primary)' : '#1976d2'
            }}>
              {customerData?.name ? 'Member' : 'Guest'}
            </div>
          </div>
        </div>

        {/* Statistiques - grille 2 colonnes comme dans l'interface client */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '16px',
          marginBottom: '20px'
        }}>
          {/* Carte points */}
          <div style={{
            background: `linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%)`,
            color: 'white',
            borderRadius: 'var(--loyalty-radius-small)',
            padding: '20px',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            transition: 'var(--loyalty-transition)',
            cursor: 'pointer'
          }}>
            <div style={{ opacity: 0.8, fontSize: '20px' }}>💎</div>
            <div>
              <div style={{ fontSize: '24px', fontWeight: 'bold' }}>
                {customerData?.points || 0}
              </div>
              <div style={{ fontSize: '12px', opacity: 0.8 }}>
                Points
              </div>
            </div>
          </div>

          {/* Carte commandes */}
          <div style={{
            background: 'var(--loyalty-surface)',
            borderRadius: 'var(--loyalty-radius-small)',
            padding: '20px',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            transition: 'var(--loyalty-transition)',
            cursor: 'pointer'
          }}>
            <div style={{ opacity: 0.8, fontSize: '20px' }}>🛍️</div>
            <div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: 'var(--loyalty-text)' }}>
                {customerData?.orders || 0}
              </div>
              <div style={{ fontSize: '12px', color: 'var(--loyalty-text-secondary)' }}>
                Orders
              </div>
            </div>
          </div>
        </div>

        {/* Barre de progression vers la prochaine récompense */}
        <div style={{
          background: 'var(--loyalty-surface)',
          borderRadius: 'var(--loyalty-radius-small)',
          padding: '20px',
          marginBottom: '20px'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '12px'
          }}>
            <div style={{
              fontSize: '14px',
              fontWeight: '600',
              color: 'var(--loyalty-text)'
            }}>
              Next reward
            </div>
            <div style={{
              fontSize: '12px',
              color: 'var(--loyalty-text-secondary)'
            }}>
              {Math.max(0, 500 - (customerData?.points || 0))} points needed
            </div>
          </div>

          {/* Barre de progression */}
          <div style={{
            width: '100%',
            height: '8px',
            background: '#e0e0e0',
            borderRadius: '4px',
            overflow: 'hidden'
          }}>
            <div style={{
              width: `${Math.min(100, ((customerData?.points || 0) / 500) * 100)}%`,
              height: '100%',
              background: `linear-gradient(90deg, var(--loyalty-primary), var(--loyalty-secondary))`,
              transition: 'width 0.3s ease'
            }} />
          </div>
        </div>

        {/* Navigation par cartes - nouvelle structure */}
        <div style={{ marginBottom: '20px' }}>
          {/* Your rewards card */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            padding: '16px',
            background: 'var(--loyalty-surface)',
            borderRadius: 'var(--loyalty-radius-small)',
            marginBottom: '12px',
            cursor: 'pointer',
            transition: 'var(--loyalty-transition)',
            border: '2px solid transparent'
          }}>
            <div style={{ flex: 1 }}>
              <div style={{
                fontSize: '16px',
                fontWeight: '600',
                color: 'var(--loyalty-text)',
                marginBottom: '4px'
              }}>
                Your rewards
              </div>
              <div style={{
                fontSize: '14px',
                color: 'var(--loyalty-text-secondary)'
              }}>
                You have 1 reward available
              </div>
            </div>
            <div style={{
              color: 'var(--loyalty-text-secondary)',
              transition: 'var(--loyalty-transition)'
            }}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="9,18 15,12 9,6"></polyline>
              </svg>
            </div>
          </div>

          {/* Ways to earn card */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            padding: '16px',
            background: 'var(--loyalty-surface)',
            borderRadius: 'var(--loyalty-radius-small)',
            marginBottom: '12px',
            cursor: 'pointer',
            transition: 'var(--loyalty-transition)',
            border: '2px solid transparent'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              background: `linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%)`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              flexShrink: 0
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 6v6l4 2"></path>
              </svg>
            </div>
            <div style={{ flex: 1 }}>
              <div style={{
                fontSize: '16px',
                fontWeight: '600',
                color: 'var(--loyalty-text)'
              }}>
                Ways to earn
              </div>
            </div>
            <div style={{
              color: 'var(--loyalty-text-secondary)',
              transition: 'var(--loyalty-transition)'
            }}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="9,18 15,12 9,6"></polyline>
              </svg>
            </div>
          </div>

          {/* Ways to redeem card */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            padding: '16px',
            background: 'var(--loyalty-surface)',
            borderRadius: 'var(--loyalty-radius-small)',
            marginBottom: '12px',
            cursor: 'pointer',
            transition: 'var(--loyalty-transition)',
            border: '2px solid transparent'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              background: `linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%)`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              flexShrink: 0
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M20 12v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6"></path>
                <path d="M2 7h20l-2 5H4l-2-5z"></path>
                <path d="M12 22V7"></path>
              </svg>
            </div>
            <div style={{ flex: 1 }}>
              <div style={{
                fontSize: '16px',
                fontWeight: '600',
                color: 'var(--loyalty-text)'
              }}>
                Ways to redeem
              </div>
            </div>
            <div style={{
              color: 'var(--loyalty-text-secondary)',
              transition: 'var(--loyalty-transition)'
            }}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="9,18 15,12 9,6"></polyline>
              </svg>
            </div>
          </div>
        </div>

        {/* Section Parrainage */}
        <div style={{
          marginBottom: '20px'
        }}>
          <div style={{
            fontSize: '16px',
            fontWeight: '600',
            marginBottom: '8px',
            color: 'var(--loyalty-text)'
          }}>
            Refer your friends
          </div>
          <div style={{
            fontSize: '14px',
            color: 'var(--loyalty-text-secondary)',
            marginBottom: '8px'
          }}>
            0 referrals completed
          </div>
          <div style={{
            fontSize: '13px',
            color: 'var(--loyalty-text-secondary)',
            marginBottom: '12px'
          }}>
            Share this URL to give your friends the reward €4 off coupon
          </div>
          <div style={{
            display: 'flex',
            gap: '8px',
            marginBottom: '12px'
          }}>
            <input
              type="text"
              value="https://refs.cc/NnoKl5oL?smile_ref"
              readOnly
              style={{
                flex: 1,
                padding: '8px 12px',
                border: '1px solid var(--loyalty-border)',
                borderRadius: 'var(--loyalty-radius-small)',
                fontSize: '12px',
                background: 'var(--loyalty-surface)'
              }}
            />
            <button style={{
              padding: '8px 12px',
              background: 'var(--loyalty-surface)',
              border: '1px solid var(--loyalty-border)',
              borderRadius: 'var(--loyalty-radius-small)',
              cursor: 'pointer'
            }}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
              </svg>
            </button>
          </div>
          <div style={{
            display: 'flex',
            gap: '8px'
          }}>
            <button style={{
              flex: 1,
              padding: '8px 12px',
              background: '#1877f2',
              color: 'white',
              border: 'none',
              borderRadius: 'var(--loyalty-radius-small)',
              fontSize: '12px',
              fontWeight: '600',
              cursor: 'pointer'
            }}>
              Facebook
            </button>
            <button style={{
              flex: 1,
              padding: '8px 12px',
              background: '#1da1f2',
              color: 'white',
              border: 'none',
              borderRadius: 'var(--loyalty-radius-small)',
              fontSize: '12px',
              fontWeight: '600',
              cursor: 'pointer'
            }}>
              X
            </button>
            <button style={{
              flex: 1,
              padding: '8px 12px',
              background: 'var(--loyalty-surface)',
              color: 'var(--loyalty-text)',
              border: '1px solid var(--loyalty-border)',
              borderRadius: 'var(--loyalty-radius-small)',
              fontSize: '12px',
              fontWeight: '600',
              cursor: 'pointer'
            }}>
              Email
            </button>
          </div>
        </div>

        {/* Your Activity card */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '16px',
          padding: '16px',
          background: 'var(--loyalty-surface)',
          borderRadius: 'var(--loyalty-radius-small)',
          marginBottom: '20px',
          cursor: 'pointer',
          transition: 'var(--loyalty-transition)',
          border: '2px solid transparent'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            background: `linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%)`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            flexShrink: 0
          }}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z"></path>
            </svg>
          </div>
          <div style={{ flex: 1 }}>
            <div style={{
              fontSize: '16px',
              fontWeight: '600',
              color: 'var(--loyalty-text)'
            }}>
              Your activity
            </div>
          </div>
          <div style={{
            color: 'var(--loyalty-text-secondary)',
            transition: 'var(--loyalty-transition)'
          }}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="9,18 15,12 9,6"></polyline>
            </svg>
          </div>
        </div>

        {/* Footer avec logo */}
        <div style={{
          textAlign: 'center',
          padding: '16px',
          background: 'var(--loyalty-surface)',
          borderRadius: 'var(--loyalty-radius-small)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px',
          border: '1px solid var(--loyalty-border)'
        }}>
          <div style={{
            width: '18px',
            height: '18px',
            background: 'var(--loyalty-primary)',
            borderRadius: '3px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <span style={{ fontSize: '10px', color: 'white' }}>😊</span>
          </div>
          <span style={{
            fontSize: '12px',
            color: 'var(--loyalty-text-secondary)',
            fontWeight: '500'
          }}>Powered by Loyalty App</span>
        </div>
      </div>
    </div>
  );
}
