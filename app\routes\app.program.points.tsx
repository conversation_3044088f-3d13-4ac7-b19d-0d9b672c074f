import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useActionData, useLoaderData, useNavigation, useSubmit } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  FormLayout,
  TextField,
  Button,
  Text,
  BlockStack,
  Banner,
  List,
  Toast,
  Frame,
  Badge,
  ButtonGroup,
  DataTable
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { AdminLayout } from "../components/Layout/AdminLayout";
import { authenticate } from "app/shopify.server";
import { getPointsSettings, updatePointsSettings, type PointsSettings } from "../models/PointsSettings.server";
import { getWaysToEarn, initializeDefaultWayToEarn, createWayToEarn, updateWayToEarn } from "../models/WayToEarn.server";
import { getWaysToRedeem, initializeDefaultWayToRedeem, createWayToRedeem, updateWayToRedeem } from "../models/WayToRedeem.server";
import { WayToEarnModal, type WayToEarnFormData } from "../components/Modals/WayToEarnModal";
import { WayToRedeemModal, type WayToRedeemFormData } from "../components/Modals/WayToRedeemModal";

interface ActionResponse {
  error?: string;
  success?: boolean;
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);

  const [settings, waysToEarn, waysToRedeem] = await Promise.all([
    getPointsSettings(session.shop),
    getWaysToEarn(session.shop),
    getWaysToRedeem(session.shop)
  ]);

  // Initialiser les ways par défaut si aucun n'existe
  let finalWaysToEarn = waysToEarn;
  let finalWaysToRedeem = waysToRedeem;

  if (waysToEarn.length === 0) {
    finalWaysToEarn = await initializeDefaultWayToEarn(session.shop);
  }

  if (waysToRedeem.length === 0) {
    finalWaysToRedeem = await initializeDefaultWayToRedeem(session.shop);
  }

  if (!settings) {
    return json({
      settings: {
        earningRate: 0,
        redemptionRate: 0,
        minimumPoints: 0,
        expirationDays: 0,
        referralPoints: 0,
        birthdayPoints: 0
      },
      waysToEarn: finalWaysToEarn,
      waysToRedeem: finalWaysToRedeem
    });
  }

  return json({
    settings,
    waysToEarn: finalWaysToEarn,
    waysToRedeem: finalWaysToRedeem
  });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);

  try {
    const formData = await request.formData();
    // Vérifier d'abord si c'est une action administrative (Ways to Earn/Redeem)
    const adminAction = formData.get("adminAction") as string;
    // Sinon, c'est une action de settings
    const actionType = formData.get("actionType") as string;

    const currentAction = adminAction || actionType;

    switch (currentAction) {
      case "updateSettings": {
        // Récupérer toutes les valeurs du formulaire
        const earningRate = formData.get("earningRate") as string;
        const redemptionRate = formData.get("redemptionRate") as string;
        const minimumPoints = formData.get("minimumPoints") as string;
        const expirationDays = formData.get("expirationDays") as string;
        const referralPoints = formData.get("referralPoints") as string;
        const birthdayPoints = formData.get("birthdayPoints") as string;

        // Validation que tous les champs sont présents
        if (!earningRate || !redemptionRate || !minimumPoints || !expirationDays || !referralPoints || !birthdayPoints) {
          return json<ActionResponse>({
            error: "Tous les champs sont requis"
          }, { status: 400 });
        }

        const data = {
          earningRate: parseFloat(earningRate),
          redemptionRate: parseFloat(redemptionRate),
          minimumPoints: parseInt(minimumPoints),
          expirationDays: parseInt(expirationDays),
          referralPoints: parseInt(referralPoints),
          birthdayPoints: parseInt(birthdayPoints),
        };

        // Validation des données
        if (isNaN(data.earningRate) || isNaN(data.redemptionRate) || isNaN(data.minimumPoints) ||
            isNaN(data.expirationDays) || isNaN(data.referralPoints) || isNaN(data.birthdayPoints)) {
          return json<ActionResponse>({
            error: "Toutes les valeurs doivent être des nombres valides"
          }, { status: 400 });
        }

        if (data.earningRate <= 0 || data.redemptionRate <= 0 || data.minimumPoints < 0 || data.expirationDays < 0) {
          return json<ActionResponse>({
            error: "Les valeurs doivent être positives"
          }, { status: 400 });
        }

        const result = await updatePointsSettings(session.shop, data);

        if (!result) {
          return json<ActionResponse>({
            error: "Erreur lors de la mise à jour des paramètres"
          }, { status: 500 });
        }

        return json<ActionResponse>({ success: true });
      }

      case "createWayToEarn": {
        const wayData = {
          name: formData.get("name") as string,
          description: formData.get("description") as string,
          actionType: formData.get("actionType") as string,
          earningType: formData.get("earningType") as "increments" | "fixed",
          earningValue: parseFloat(formData.get("earningValue") as string),
          icon: formData.get("icon") as string,
          isActive: formData.get("isActive") === "true"
        };

        const result = await createWayToEarn(session.shop, wayData);
        if (!result) {
          return json<ActionResponse>({
            error: "Erreur lors de la création"
          }, { status: 500 });
        }

        return json<ActionResponse>({ success: true });
      }

      case "updateWayToEarn": {
        const id = formData.get("id") as string;
        const wayData = {
          name: formData.get("name") as string,
          description: formData.get("description") as string,
          actionType: formData.get("actionType") as string,
          earningType: formData.get("earningType") as "increments" | "fixed",
          earningValue: parseFloat(formData.get("earningValue") as string),
          icon: formData.get("icon") as string,
          isActive: formData.get("isActive") === "true"
        };

        const result = await updateWayToEarn(id, session.shop, wayData);
        if (!result) {
          return json<ActionResponse>({
            error: "Erreur lors de la mise à jour"
          }, { status: 500 });
        }

        return json<ActionResponse>({ success: true });
      }

      case "createWayToRedeem": {
        const wayData = {
          name: formData.get("name") as string,
          description: formData.get("description") as string,
          redeemType: formData.get("redeemType") as "discount" | "product" | "shipping",
          redeemValue: parseFloat(formData.get("redeemValue") as string),
          pointsCost: parseInt(formData.get("pointsCost") as string),
          icon: formData.get("icon") as string,
          isActive: formData.get("isActive") === "true"
        };

        const result = await createWayToRedeem(session.shop, wayData);
        if (!result) {
          return json<ActionResponse>({
            error: "Erreur lors de la création"
          }, { status: 500 });
        }

        return json<ActionResponse>({ success: true });
      }

      case "updateWayToRedeem": {
        const id = formData.get("id") as string;
        const wayData = {
          name: formData.get("name") as string,
          description: formData.get("description") as string,
          redeemType: formData.get("redeemType") as "discount" | "product" | "shipping",
          redeemValue: parseFloat(formData.get("redeemValue") as string),
          pointsCost: parseInt(formData.get("pointsCost") as string),
          icon: formData.get("icon") as string,
          isActive: formData.get("isActive") === "true"
        };

        const result = await updateWayToRedeem(id, session.shop, wayData);
        if (!result) {
          return json<ActionResponse>({
            error: "Erreur lors de la mise à jour"
          }, { status: 500 });
        }

        return json<ActionResponse>({ success: true });
      }

      default:
        return json<ActionResponse>({
          error: "Action non reconnue"
        }, { status: 400 });
    }
  } catch (error) {
    console.error("Erreur lors de l'action :", error);
    return json<ActionResponse>({
      error: "Erreur lors de l'action"
    }, { status: 500 });
  }
};

export default function PointsSettings() {
  const { settings, waysToEarn, waysToRedeem } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const submit = useSubmit();
  const isLoading = navigation.state === "submitting";

  const [formState, setFormState] = useState<PointsSettings>(settings);
  const [previewAmount, setPreviewAmount] = useState("100");
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  // États des modals
  const [earnModalOpen, setEarnModalOpen] = useState(false);
  const [redeemModalOpen, setRedeemModalOpen] = useState(false);
  const [editingWayToEarn, setEditingWayToEarn] = useState<any>(null);
  const [editingWayToRedeem, setEditingWayToRedeem] = useState<any>(null);

  // Mettre à jour le formulaire quand les settings changent
  useEffect(() => {
    setFormState(settings);
  }, [settings]);

  // Afficher le toast en cas de succès ou d'erreur
  useEffect(() => {
    if (actionData?.success) {
      setToastMessage("Paramètres mis à jour avec succès");
      setToastActive(true);
    } else if (actionData?.error) {
      setToastMessage(actionData.error);
      setToastActive(true);
    }
  }, [actionData]);

  // Calculer les points pour le montant de prévisualisation
  const previewPoints = Math.round(parseFloat(previewAmount) * formState.earningRate);
  const previewValue = Math.round(previewPoints * formState.redemptionRate * 100) / 100;

  const handleUpdateSettings = (field: string, value: string) => {
    setFormState(prev => ({ ...prev, [field]: Number(value) }));
  };

  const handleSubmit = useCallback((event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const form = event.currentTarget;
    const formData = new FormData(form);
    formData.append("actionType", "updateSettings");
    submit(formData, { method: "post" });
  }, [submit]);

  // Gestionnaires des modals
  const handleSaveWayToEarn = useCallback((data: WayToEarnFormData) => {
    const formData = new FormData();
    // Action administrative (ce que fait l'admin)
    formData.append("adminAction", editingWayToEarn ? "updateWayToEarn" : "createWayToEarn");
    if (editingWayToEarn) {
      formData.append("id", editingWayToEarn.id);
    }
    formData.append("name", data.name);
    formData.append("description", data.description);
    // Type d'action client (ce que fait le client pour gagner des points)
    formData.append("actionType", data.actionType);
    formData.append("earningType", data.earningType);
    formData.append("earningValue", data.earningValue);
    formData.append("icon", data.icon);
    formData.append("isActive", data.isActive.toString());

    submit(formData, { method: "post" });
    setEarnModalOpen(false);
    setEditingWayToEarn(null);
  }, [submit, editingWayToEarn]);

  const handleSaveWayToRedeem = useCallback((data: WayToRedeemFormData) => {
    const formData = new FormData();
    // Action administrative (ce que fait l'admin)
    formData.append("adminAction", editingWayToRedeem ? "updateWayToRedeem" : "createWayToRedeem");
    if (editingWayToRedeem) {
      formData.append("id", editingWayToRedeem.id);
    }
    formData.append("name", data.name);
    formData.append("description", data.description);
    formData.append("redeemType", data.redeemType);
    formData.append("redeemValue", data.redeemValue);
    formData.append("pointsCost", data.pointsCost);
    formData.append("icon", data.icon);
    formData.append("isActive", data.isActive.toString());

    submit(formData, { method: "post" });
    setRedeemModalOpen(false);
    setEditingWayToRedeem(null);
  }, [submit, editingWayToRedeem]);

  const toastMarkup = toastActive ? (
    <Toast
      content={toastMessage}
      onDismiss={() => setToastActive(false)}
      duration={4000}
    />
  ) : null;

  return (
    <Frame>
      <AdminLayout>
        <Page title="Configuration des points">
          <Layout>
            {actionData?.error && (
              <Layout.Section>
                <Banner tone="critical">
                  {actionData.error}
                </Banner>
              </Layout.Section>
            )}

            <Layout.Section>
              <form onSubmit={handleSubmit}>
                <Card>
                  <BlockStack gap="400">
                    <Text as="h2" variant="headingMd">Paramètres de base</Text>

                    <FormLayout>
                      <FormLayout.Group>
                        <TextField
                          label="Taux de gain (points/€)"
                          type="number"
                          name="earningRate"
                          value={String(formState.earningRate)}
                          onChange={(value) => handleUpdateSettings("earningRate", value)}
                          autoComplete="off"
                          step={0.1}
                          min="0"
                        />
                        <TextField
                          label="Taux de conversion (points/€)"
                          type="number"
                          name="redemptionRate"
                          value={String(formState.redemptionRate)}
                          onChange={(value) => handleUpdateSettings("redemptionRate", value)}
                          autoComplete="off"
                          step={1}
                          min="0"
                          helpText="Nombre de points nécessaires pour 1€ de réduction (ex: 100 points = 1€)"
                        />
                      </FormLayout.Group>

                      <FormLayout.Group>
                        <TextField
                          label="Points minimum pour échanger"
                          type="number"
                          name="minimumPoints"
                          value={String(formState.minimumPoints)}
                          onChange={(value) => handleUpdateSettings("minimumPoints", value)}
                          autoComplete="off"
                          min="0"
                        />
                        <TextField
                          label="Expiration des points (jours)"
                          type="number"
                          name="expirationDays"
                          value={String(formState.expirationDays)}
                          onChange={(value) => handleUpdateSettings("expirationDays", value)}
                          autoComplete="off"
                          min="0"
                        />
                      </FormLayout.Group>

                      <FormLayout.Group>
                        <TextField
                          label="Points de parrainage"
                          type="number"
                          name="referralPoints"
                          value={String(formState.referralPoints)}
                          onChange={(value) => handleUpdateSettings("referralPoints", value)}
                          autoComplete="off"
                          min="0"
                        />
                        <TextField
                          label="Points d'anniversaire"
                          type="number"
                          name="birthdayPoints"
                          value={String(formState.birthdayPoints)}
                          onChange={(value) => handleUpdateSettings("birthdayPoints", value)}
                          autoComplete="off"
                          min="0"
                        />
                      </FormLayout.Group>

                      <Button variant="primary" submit loading={isLoading}>
                        Enregistrer
                      </Button>
                    </FormLayout>
                  </BlockStack>
                </Card>
              </form>
            </Layout.Section>

            <Layout.Section variant="oneThird">
              <BlockStack gap="400">
                <Card>
                  <BlockStack gap="400">
                    <Text as="h2" variant="headingMd">Prévisualisation</Text>
                    <TextField
                      label="Montant d'achat (€)"
                      type="number"
                      value={previewAmount}
                      onChange={setPreviewAmount}
                      autoComplete="off"
                      min="0"
                    />
                    <BlockStack gap="200">
                      <Text as="p" variant="bodyMd">
                        Pour un achat de {previewAmount}€ :
                      </Text>
                      <List type="bullet">
                        <List.Item>
                          Points gagnés : {previewPoints} points
                        </List.Item>
                        <List.Item>
                          Valeur en € : {previewValue}€
                        </List.Item>
                      </List>
                    </BlockStack>
                  </BlockStack>
                </Card>
              </BlockStack>
            </Layout.Section>

            {/* Section Ways to Earn */}
            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <Text as="h2" variant="headingMd">Façons de gagner des points</Text>
                    <Button
                      variant="primary"
                      onClick={() => {
                        setEditingWayToEarn(null);
                        setEarnModalOpen(true);
                      }}
                    >
                      Ajouter une façon de gagner
                    </Button>
                  </div>

                  <Text as="p" variant="bodyMd" tone="subdued">
                    Configurez les différentes façons dont vos clients peuvent gagner des points.
                    Vous pouvez créer des actions avec des <strong>points par euro dépensé</strong> (ex: 5 points/€1)
                    ou des <strong>points fixes</strong> pour des actions spécifiques (ex: 100 points pour l'inscription).
                  </Text>

                  <div style={{ display: "flex", flexWrap: "wrap", gap: "16px" }}>
                    {waysToEarn.slice(0, 5).map((way: any) => {
                      const getEarningDisplay = () => {
                        if (way.earningType === "increments") {
                          return `${way.earningValue} points par €1 dépensé`;
                        } else {
                          return `${way.earningValue} points fixes`;
                        }
                      };

                      return (
                        <Card key={way.id}>
                          <div style={{ padding: "16px" }}>
                            <BlockStack gap="200">
                              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                                <span>📦</span>
                                <Text as="h3" variant="headingSm">{way.name}</Text>
                                {way.isActive ? (
                                  <Badge tone="success">Actif</Badge>
                                ) : (
                                  <Badge tone="critical">Inactif</Badge>
                                )}
                              </div>
                              <Text as="p" variant="bodyMd" tone="subdued">
                                {way.description}
                              </Text>
                              <Text as="p" variant="bodyMd">
                                <strong>{getEarningDisplay()}</strong>
                              </Text>
                              <Button
                                size="micro"
                                onClick={() => {
                                  setEditingWayToEarn(way);
                                  setEarnModalOpen(true);
                                }}
                              >
                                Modifier
                              </Button>
                            </BlockStack>
                          </div>
                        </Card>
                      );
                    })}
                  </div>

                  {waysToEarn.length > 0 && (
                    <div style={{ textAlign: "center" }}>
                      <Button url="/app/program/points/actions">
                        {waysToEarn.length > 5
                          ? `Voir toutes les façons de gagner (${waysToEarn.length})`
                          : "Voir toutes les façons de gagner"
                        }
                      </Button>
                    </div>
                  )}
                </BlockStack>
              </Card>
            </Layout.Section>

            {/* Section Ways to Redeem */}
            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <Text as="h2" variant="headingMd">Façons d'échanger des points</Text>
                    <Button
                      variant="primary"
                      onClick={() => {
                        setEditingWayToRedeem(null);
                        setRedeemModalOpen(true);
                      }}
                    >
                      Ajouter une façon d'échanger
                    </Button>
                  </div>

                  <Text as="p" variant="bodyMd" tone="subdued">
                    Configurez les différentes récompenses que vos clients peuvent obtenir en échangeant leurs points.
                    Par exemple, des réductions sur leurs commandes, des produits gratuits, ou la livraison gratuite.
                  </Text>

                  <div style={{ display: "flex", flexWrap: "wrap", gap: "16px" }}>
                    {waysToRedeem.slice(0, 5).map((way: any) => (
                      <Card key={way.id}>
                        <div style={{ padding: "16px" }}>
                          <BlockStack gap="200">
                            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                              <span>💰</span>
                              <Text as="h3" variant="headingSm">{way.name}</Text>
                              {way.isActive ? (
                                <Badge tone="success">Actif</Badge>
                              ) : (
                                <Badge tone="critical">Inactif</Badge>
                              )}
                            </div>
                            <Text as="p" variant="bodyMd" tone="subdued">
                              {way.description}
                            </Text>
                            <Text as="p" variant="bodyMd">
                              <strong>{way.pointsCost} points</strong>
                            </Text>
                            <Button
                              size="micro"
                              onClick={() => {
                                setEditingWayToRedeem(way);
                                setRedeemModalOpen(true);
                              }}
                            >
                              Modifier
                            </Button>
                          </BlockStack>
                        </div>
                      </Card>
                    ))}
                  </div>

                  {waysToRedeem.length > 0 && (
                    <div style={{ textAlign: "center" }}>
                      <Button url="/app/program/points/actions">
                        {waysToRedeem.length > 5
                          ? `Voir toutes les façons d'échanger (${waysToRedeem.length})`
                          : "Voir toutes les façons d'échanger"
                        }
                      </Button>
                    </div>
                  )}
                </BlockStack>
              </Card>
            </Layout.Section>
          </Layout>
          {toastMarkup}
        </Page>
      </AdminLayout>

      {/* Modals */}
      <WayToEarnModal
        isOpen={earnModalOpen}
        onClose={() => {
          setEarnModalOpen(false);
          setEditingWayToEarn(null);
        }}
        onSave={handleSaveWayToEarn}
        wayToEarn={editingWayToEarn}
        isLoading={isLoading}
      />

      <WayToRedeemModal
        isOpen={redeemModalOpen}
        onClose={() => {
          setRedeemModalOpen(false);
          setEditingWayToRedeem(null);
        }}
        onSave={handleSaveWayToRedeem}
        wayToRedeem={editingWayToRedeem}
        isLoading={isLoading}
      />
    </Frame>
  );
}
