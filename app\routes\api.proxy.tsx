import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { getCustomerByShopifyId, getCustomerById, promoteToMember, createCustomer, updateCustomerPoints } from "app/models/Customer.server";
import { getWaysToEarn } from "app/models/WayToEarn.server";
import { getWaysToRedeem } from "app/models/WayToRedeem.server";
import { getExchangeableProducts } from "app/models/ExchangeableProducts.server";
import { awardPointsForSignup } from "app/services/pointsService.server";
import { getPointsSettings } from "app/models/PointsSettings.server";
import { getProgramSettings } from "app/models/ProgramSettings.server";
import { getSiteSettings } from "app/models/SiteSettings.server";
import prisma from "app/db.server";

/**
 * Routeur API centralisé pour l'App Proxy Shopify
 * Toutes les communications entre le widget et le backend passent par ici
 */

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const shop = url.searchParams.get("shop");
  const path = url.searchParams.get("path_prefix") || "";
  const prepath = url.searchParams.get("prepath") || "";
  console.log("prepath", prepath);
  // Extraire l'endpoint depuis le path
  // const pathParts = path.split("/").filter(Boolean);
  // const endpoint = pathParts[0] || "widget";
  //   console.log("ap")
  if (!shop) {
    return json({ error: "Shop parameter required" }, { status: 400 });
  }

  try {
    switch (prepath) {
      case "widget":
        return await handleWidgetRequest(request, shop);

      case "customer":
        return await handleCustomerRequest(request, shop);

      case "ways-to-earn":
        return await handleWaysToEarnRequest(request, shop);

      case "ways-to-redeem":
        return await handleWaysToRedeemRequest(request, shop);

      case "program-info":
        return await handleProgramInfoRequest(request, shop);

      case "client-products":
        return await handleClientProductsRequest(request, shop);

      default:
        return await handleWidgetRequest(request, shop);
    }
  } catch (error) {
    console.error("Erreur dans l'API proxy:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const url = new URL(request.url);
  const shop = url.searchParams.get("shop");
  const path = url.searchParams.get("path_prefix") || "";
  const prepath = url.searchParams.get("prepath") || "";

  // Extraire l'endpoint depuis le path
  // const pathParts = path.split("/").filter(Boolean);
  // const endpoint = pathParts[0] || "";

  if (!shop) {
    return json({ error: "Shop parameter required" }, { status: 400 });
  }

  try {
    switch (prepath) {
      case "signup":
        return await handleSignupRequest(request, shop);

      case "client-redeem":
        return await handleClientRedeemRequest(request, shop);

      default:
        return json({ error: "Endpoint not found" }, { status: 404 });
    }
  } catch (error) {
    console.error("Erreur dans l'API proxy action:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};

/**
 * Handler pour le widget principal (affichage HTML)
 */
async function handleWidgetRequest(request: Request, shop: string) {
  const url = new URL(request.url);
  const customerId = url.searchParams.get("logged_in_customer_id");

  // Récupérer les paramètres de style pour le widget
  const siteSettings = await getSiteSettings(shop);

  // Si pas de client connecté, afficher un widget de connexion
  if (!customerId) {
    const html = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Programme de fidélité</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .loyalty-widget {
            background: white;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 400px;
            margin: 0 auto;
        }
        .loyalty-widget h3 {
            color: ${siteSettings?.widgetColor || '#2e7d32'};
            margin-bottom: 16px;
        }
        .loyalty-button {
            background: ${siteSettings?.widgetColor || '#2e7d32'};
            color: ${siteSettings?.widgetTextColor || 'white'};
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 16px;
        }
        .loyalty-button:hover {
            background: ${siteSettings?.widgetSecondaryColor || '#1b5e20'};
        }
    </style>
</head>
<body>
    <div class="loyalty-widget">
        <h3>Programme de fidélité</h3>
        <p>Connectez-vous pour accéder à vos points et récompenses.</p>
        <a href="/account/login" class="loyalty-button">Se connecter</a>
    </div>
</body>
</html>
    `;

    return new Response(html, {
      headers: { "Content-Type": "text/html" },
    });
  }

  try {
    // Récupérer les données du client
    const customer = await getCustomerByShopifyId(customerId, shop);

    const html = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Programme de fidélité</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .loyalty-widget {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 400px;
            margin: 0 auto;
        }
        .loyalty-widget h3 {
            color: ${siteSettings?.widgetColor || '#2e7d32'};
            margin-bottom: 16px;
            text-align: center;
        }
        .points-display {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 16px 0;
        }
        .points-value {
            font-size: 32px;
            font-weight: bold;
            color: ${siteSettings?.widgetColor || '#2e7d32'};
            margin-bottom: 4px;
        }
        .points-label {
            color: #666;
            font-size: 14px;
        }
        .customer-info {
            margin: 16px 0;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .customer-info p {
            margin: 4px 0;
            font-size: 14px;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .badge.member {
            background: #e8f5e8;
            color: ${siteSettings?.widgetColor || '#2e7d32'};
        }
        .badge.guest {
            background: #e3f2fd;
            color: #1976d2;
        }
        .loyalty-button {
            background: ${siteSettings?.widgetColor || '#2e7d32'};
            color: ${siteSettings?.widgetTextColor || 'white'};
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: block;
            text-align: center;
            margin-top: 16px;
            width: 100%;
            box-sizing: border-box;
        }
        .loyalty-button:hover {
            background: ${siteSettings?.widgetSecondaryColor || '#1b5e20'};
        }
    </style>
</head>
<body>
    <div class="loyalty-widget">
        <h3>Programme de fidélité</h3>

        ${customer ? `
            <div class="customer-info">
                <p><strong>Statut:</strong> <span class="badge ${customer.type}">${customer.type === 'member' ? 'Membre' : 'Invité'}</span></p>
                <p><strong>Commandes:</strong> ${customer.ordersCount}</p>
                <p><strong>Total dépensé:</strong> ${customer.totalSpent.toFixed(2)}€</p>
            </div>

            <div class="points-display">
                <div class="points-value">${customer.points}</div>
                <div class="points-label">Points disponibles</div>
            </div>

            <a href="/apps/proxy/loyalty?shop=${shop}&customer_id=${customerId}" class="loyalty-button">
                Voir mes récompenses
            </a>
        ` : `
            <p>Impossible de charger vos informations.</p>
            <a href="/account" class="loyalty-button">Mon compte</a>
        `}
    </div>
</body>
</html>
    `;

    return new Response(html, {
      headers: { "Content-Type": "text/html" },
    });
  } catch (error) {
    console.error("Error in app proxy:", error);

    const errorHtml = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erreur - Programme de fidélité</title>
</head>
<body>
    <div style="padding: 20px; text-align: center;">
        <h3>Erreur</h3>
        <p>Une erreur est survenue lors du chargement du programme de fidélité.</p>
    </div>
</body>
</html>
    `;

    return new Response(errorHtml, {
      headers: { "Content-Type": "text/html" },
      status: 500,
    });
  }
}

/**
 * Handler pour récupérer les données client (JSON)
 */
async function handleCustomerRequest(request: Request, shop: string) {
  const url = new URL(request.url);
  const customerId = url.searchParams.get("logged_in_customer_id");

  if (!customerId) {
    return json({
      id: null,
      customerId: null,
      firstName: null,
      lastName: null,
      email: null,
      type: "guest",
      points: 0,
      vipLevel: null,
      totalSpent: 0,
      ordersCount: 0,
      joinedAt: new Date(),
      recentHistory: [],
      referralsCount: 0
    });
  }

  try {
    // D'abord, récupérer le client par son ID Shopify
    const basicCustomer = await getCustomerByShopifyId(customerId, shop);

    if (!basicCustomer) {
      // Si le client n'existe pas dans notre DB, créer un profil guest
      return json({
        id: null,
        customerId: customerId,
        firstName: null,
        lastName: null,
        email: null,
        type: "guest",
        points: 0,
        vipLevel: null,
        totalSpent: 0,
        ordersCount: 0,
        joinedAt: new Date(),
        recentHistory: [],
        referralsCount: 0
      });
    }

    // Récupérer les données complètes avec relations
    const customer = await getCustomerById(basicCustomer.id, shop);

    if (!customer) {
      return json({ error: "Customer not found" }, { status: 404 });
    }

    // Calculer les statistiques supplémentaires
    const recentHistory = customer.history ? customer.history.slice(0, 5) : [];
    const referralsCount = customer.referrals ? customer.referrals.filter((r: any) => r.status === "completed").length : 0;

    // Retourner les données complètes pour l'embed
    const customerData = {
      id: customer.id,
      customerId: customer.customerId,
      firstName: customer.firstName,
      lastName: customer.lastName,
      email: customer.email,
      type: customer.type,
      points: customer.points,
      vipLevel: customer.vipLevel,
      totalSpent: customer.totalSpent,
      ordersCount: customer.ordersCount,
      joinedAt: customer.joinedAt,
      recentHistory: recentHistory.map((h: any) => ({
        action: h.action,
        points: h.points,
        description: h.description,
        timestamp: h.timestamp
      })),
      referralsCount
    };

    return json(customerData);
  } catch (error) {
    console.error("Error fetching customer data:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Handler pour récupérer les façons de gagner des points
 */
async function handleWaysToEarnRequest(_request: Request, shop: string) {
  try {
    // Récupérer les façons de gagner des points actives
    const waysToEarn = await getWaysToEarn(shop);

    // Filtrer seulement les actives et formater pour l'interface client
    const activeWays = waysToEarn
      .filter(way => way.isActive)
      .map(way => ({
        id: way.id,
        name: way.name,
        description: way.description,
        actionType: way.actionType,
        earningType: way.earningType,
        earningValue: way.earningValue,
        icon: way.icon
      }));

    return json(activeWays);
  } catch (error) {
    console.error("Error fetching ways to earn:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Handler pour récupérer les façons d'échanger des points
 */
async function handleWaysToRedeemRequest(_request: Request, shop: string) {
  try {
    // Récupérer les façons d'échanger des points actives
    const waysToRedeem = await getWaysToRedeem(shop);

    // Filtrer seulement les actives et formater pour l'interface client
    const activeWays = waysToRedeem
      .filter(way => way.isActive)
      .map(way => ({
        id: way.id,
        name: way.name,
        description: way.description,
        redeemType: way.redeemType,
        redeemValue: way.redeemValue,
        pointsCost: way.pointsCost,
        icon: way.icon
      }))
      .sort((a, b) => a.pointsCost - b.pointsCost); // Trier par coût croissant

    return json(activeWays);
  } catch (error) {
    console.error("Error fetching ways to redeem:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Handler pour l'inscription au programme de fidélité
 */
async function handleSignupRequest(request: Request, shop: string) {
  const url = new URL(request.url);
  const customerId = url.searchParams.get("logged_in_customer_id");

  if (!customerId) {
    return json({ error: "Customer ID required" }, { status: 400 });
  }

  try {
    // Promouvoir le client en membre
    const updatedCustomer = await promoteToMember(customerId, shop);

    if (!updatedCustomer) {
      const newCustomer = await createCustomer({
        customerId: customerId,
        shop: shop,
        type: "member"
      });
      return json({ error: "Customer not found" }, { status: 404 });
    }

    // Attribuer les points de bienvenue
    const signupResult = await awardPointsForSignup(shop, customerId);

    const response = {
      success: true,
      customer: {
        id: updatedCustomer.id,
        customerId: updatedCustomer.customerId,
        firstName: updatedCustomer.firstName,
        lastName: updatedCustomer.lastName,
        email: updatedCustomer.email,
        type: updatedCustomer.type,
        points: updatedCustomer.points,
        totalSpent: updatedCustomer.totalSpent,
        ordersCount: updatedCustomer.ordersCount
      },
      pointsAwarded: signupResult?.points || 0,
      message: `Bienvenue dans notre programme de fidélité ! Vous avez reçu ${signupResult?.points || 0} points de bienvenue.`
    };

    return json(response);
  } catch (error) {
    console.error("Error during loyalty signup:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Handler pour récupérer les informations du programme
 */
async function handleProgramInfoRequest(_request: Request, shop: string) {
  try {
    // Récupérer les settings du programme et les settings complets
    const [programSettings, settings, siteSettings] = await Promise.all([
      getProgramSettings(),
      prisma.settings.findUnique({
        where: { shop }
        // Récupérer TOUS les paramètres pour l'interface client
      }),
      getSiteSettings(shop) // Récupérer les paramètres de style
    ]);

    const programInfo = {
      isActive: programSettings?.status || false,
      name: programSettings?.name || "Programme de fidélité",
      description: programSettings?.description || "Gagnez des points à chaque achat !",
      widgetEnabled: settings?.widgetEnabled !== false,

      // Paramètres de style complets
      primaryColor: settings?.primaryColor || "#2E7D32",
      language: settings?.language || "fr",

      // Paramètres de style depuis SiteSettings (vrais paramètres configurés)
      widgetColor: siteSettings?.widgetColor || settings?.primaryColor || "#2E7D32",
      widgetSecondaryColor: siteSettings?.widgetSecondaryColor || "#4CAF50",
      widgetTextColor: siteSettings?.widgetTextColor || "#FFFFFF",
      widgetPosition: siteSettings?.widgetPosition || "bottom-right",
      widgetSize: siteSettings?.widgetSize || "medium",
      widgetBorderRadius: siteSettings?.widgetBorderRadius || "rounded",
      widgetShadow: siteSettings?.widgetShadow !== false,
      widgetAnimation: siteSettings?.widgetAnimation !== false,
      showPointsOnButton: siteSettings?.showPointsOnButton !== false,

      // Autres paramètres configurés
      pointsName: siteSettings?.pointsName || "Points",
      welcomeMessage: siteSettings?.welcomeMessage || "Bienvenue dans notre programme de fidélité !",
      shopName: siteSettings?.shopName || shop,
      currency: siteSettings?.currency || "EUR",
      customCSS: siteSettings?.customCSS || ""
    };

    return json(programInfo);
  } catch (error) {
    console.error("Error fetching program info:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Handler pour récupérer les produits échangeables côté client
 */
async function handleClientProductsRequest(request: Request, shop: string) {
  try {
    // Récupérer les produits échangeables et les paramètres du programme
    const [exchangeableProducts, settings] = await Promise.all([
      getExchangeableProducts(shop),
      getSiteSettings(shop)
    ]);

    // Calculer le coût en points basé sur les paramètres du programme
    const productsWithPoints = exchangeableProducts
      .filter(product => product.active) // Seulement les produits actifs
      .map(product => ({
        ...product,
        pointsCost: settings?.redemptionRate
          ? Math.ceil(parseFloat(product.price) * settings.redemptionRate)
          : product.pointsCost || 100
      }));

    return json({
      products: productsWithPoints,
      programInfo: {
        pointsName: settings?.pointsName || 'Points',
        redemptionRate: settings?.redemptionRate || 0.01
      }
    });
  } catch (error) {
    console.error("Error loading client products:", error);
    return json({
      products: [],
      programInfo: { pointsName: 'Points', redemptionRate: 0.01 },
      error: "Erreur lors du chargement"
    }, { status: 500 });
  }
}

/**
 * Handler pour l'échange de produits côté client
 */
async function handleClientRedeemRequest(request: Request, shop: string) {
  try {
    const formData = await request.formData();
    const customerId = formData.get('customerId') as string;
    const productId = formData.get('productId') as string;

    if (!customerId || !productId) {
      return json({ error: "Paramètres manquants" }, { status: 400 });
    }

    // Récupérer les données du client
    const customerData = await getCustomerByShopifyId(customerId, shop);
    if (!customerData) {
      return json({ error: "Client non trouvé" }, { status: 404 });
    }

    // Récupérer le produit et les paramètres
    const [exchangeableProducts, settings] = await Promise.all([
      getExchangeableProducts(shop),
      getSiteSettings(shop)
    ]);

    const product = exchangeableProducts.find(p => p.id === productId);
    if (!product || !product.active) {
      return json({ error: "Produit non trouvé ou non disponible" }, { status: 404 });
    }

    // Calculer le coût en points
    const pointsCost = settings?.redemptionRate
      ? Math.ceil(parseFloat(product.price) * settings.redemptionRate)
      : product.pointsCost || 100;

    // Vérifier si le client a assez de points
    if (customerData.points < pointsCost) {
      return json({
        error: "Points insuffisants",
        required: pointsCost,
        available: customerData.points
      }, { status: 400 });
    }

    // Déduire les points
    const newPoints = customerData.points - pointsCost;
    await updateCustomerPoints(customerId, shop, newPoints);

    // TODO: Ici vous pourriez ajouter la logique pour créer un bon de réduction Shopify
    // ou envoyer un email au client avec les instructions

    return json({
      success: true,
      message: `Produit "${product.title}" échangé avec succès !`,
      pointsUsed: pointsCost,
      remainingPoints: newPoints,
      product: {
        title: product.title,
        image: product.image
      }
    });

  } catch (error) {
    console.error("Error redeeming product:", error);
    return json({ error: "Erreur lors de l'échange" }, { status: 500 });
  }
}
