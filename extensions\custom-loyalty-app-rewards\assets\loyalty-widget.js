/**
 * Widget de fidélité - Interface client
 * Version: 1.0.0
 */

/**
 * Formate un nombre pour l'affichage compact (1k, 1.2k, 1M, etc.)
 */
function formatCompactNumber(num) {
  if (num < 1000) return num.toString();

  if (num < 1000000) {
    const k = num / 1000;
    return k % 1 === 0 ? `${k}k` : `${k.toFixed(1)}k`;
  }

  if (num < 1000000000) {
    const m = num / 1000000;
    return m % 1 === 0 ? `${m}M` : `${m.toFixed(1)}M`;
  }

  const b = num / 1000000000;
  return b % 1 === 0 ? `${b}B` : `${b.toFixed(1)}B`;
}

class LoyaltyWidget {
  constructor(options = {}) {
    this.options = {
      shop: options.shop || '',
      customerId: options.customerId || null,
      position: options.position || 'bottom-right',
      primaryColor: options.primaryColor || '#2E7D32',
      secondaryColor: options.secondaryColor || '#4CAF50',
      autoOpen: options.autoOpen || false,
      showOnMobile: options.showOnMobile !== false,
      ...options
    };

    // URL de l'App Proxy Shopify (stable)
    this.apiBaseUrl = `/apps/proxy`;

    this.isOpen = false;
    this.isLoading = false;
    this.customerData = null;
    this.earnWays = [];
    this.redeemWays = [];
    this.nextRewardThreshold = 500; // Par défaut, sera calculé dynamiquement
    this.programInfo = null;
    this.currentView = 'main'; // 'main', 'rewards', 'history'

    this.init();
  }

  init() {
    this.createElements();
    this.bindEvents();
    this.applyCustomStyles();
    this.loadCustomerData();

    // Auto-ouverture si configuré
    if (this.options.autoOpen && !this.hasSeenWidget()) {
      setTimeout(() => this.open(), 2000);
      this.markWidgetSeen();
    }

  }

  createElements() {
    this.container = document.getElementById('loyalty-widget');
    this.trigger = document.getElementById('loyalty-trigger');
    this.panel = document.getElementById('loyalty-panel');
    this.overlay = document.getElementById('loyalty-overlay');
    this.closeBtn = document.getElementById('loyalty-close');

    // Éléments de contenu
    this.pointsPreview = document.getElementById('points-preview');
    this.loadingState = document.getElementById('loyalty-loading');
    this.guestState = document.getElementById('loyalty-guest');
    this.memberState = document.getElementById('loyalty-member');
    this.errorState = document.getElementById('loyalty-error');

    // Éléments de données client
    this.customerInitials = document.getElementById('customer-initials');
    this.customerName = document.getElementById('customer-name');
    this.customerStatus = document.getElementById('customer-status');
    this.customerPoints = document.getElementById('customer-points');
    this.customerOrders = document.getElementById('customer-orders');
    this.pointsNeeded = document.getElementById('points-needed');
    this.progressFill = document.getElementById('progress-fill');
    this.earnWaysList = document.getElementById('earn-ways');

    // Boutons d'action
    this.viewRewardsBtn = document.getElementById('view-rewards');
    this.viewHistoryBtn = document.getElementById('view-history');
    this.backFromRewardsBtn = document.getElementById('back-from-rewards');
    this.backFromHistoryBtn = document.getElementById('back-from-history');

    // Sections
    this.mainSection = document.getElementById('loyalty-main-section');
    this.rewardsSection = document.getElementById('loyalty-rewards-section');
    this.historySection = document.getElementById('loyalty-history-section');
    this.couponConfigSection = document.getElementById('loyalty-coupon-config-section');
    this.couponResultSection = document.getElementById('loyalty-coupon-result-section');

    // Nouvelles sections
    this.yourRewardsSection = document.getElementById('loyalty-your-rewards-section');
    this.waysToEarnSection = document.getElementById('loyalty-ways-to-earn-section');
    this.yourActivitySection = document.getElementById('loyalty-your-activity-section');

    // Listes
    this.rewardsList = document.getElementById('rewards-list');
    this.historyList = document.getElementById('history-list');
    this.yourRewardsList = document.getElementById('your-rewards-list');
    this.pastRewardsList = document.getElementById('past-rewards-list');
    this.waysToEarnList = document.getElementById('ways-to-earn-list');
    this.activityList = document.getElementById('activity-list');

    // Éléments de coupon
    this.backFromCouponConfigBtn = document.getElementById('back-from-coupon-config');
    this.backFromCouponResultBtn = document.getElementById('back-from-coupon-result');
    this.customerPointsHeader = document.getElementById('customer-points-header');
    this.customerPointsResult = document.getElementById('customer-points-result');
    this.pointsInput = document.getElementById('points-input');
    this.couponValueDisplay = document.getElementById('coupon-value-display');
    this.redeemCouponBtn = document.getElementById('redeem-coupon-btn');
    this.generatedCouponCode = document.getElementById('generated-coupon-code');
    this.copyCouponCodeBtn = document.getElementById('copy-coupon-code');
    this.applyCouponBtn = document.getElementById('apply-coupon-btn');

    // Nouveaux boutons de retour
    this.backFromYourRewardsBtn = document.getElementById('back-from-your-rewards');
    this.backFromWaysToEarnBtn = document.getElementById('back-from-ways-to-earn');
    this.backFromYourActivityBtn = document.getElementById('back-from-your-activity');

    // Éléments de parrainage
    this.referralLink = document.getElementById('referral-link');
    this.copyReferralBtn = document.getElementById('copy-referral-link');
    this.shareFacebookBtn = document.getElementById('share-facebook');
    this.shareTwitterBtn = document.getElementById('share-twitter');
    this.shareEmailBtn = document.getElementById('share-email');
    this.referralCount = document.getElementById('referral-count');

    // Appliquer la position
    this.container.classList.add(`position-${this.options.position}`);
  }

  bindEvents() {
    // Ouverture/fermeture du widget
    this.trigger.addEventListener('click', () => this.toggle());
    this.closeBtn.addEventListener('click', () => this.close());
    this.overlay.addEventListener('click', () => this.close());

    // Actions
    this.viewRewardsBtn?.addEventListener('click', () => this.showRewardsSection());
    this.viewHistoryBtn?.addEventListener('click', () => this.showHistorySection());
    this.backFromRewardsBtn?.addEventListener('click', () => this.showMainSection());
    this.backFromHistoryBtn?.addEventListener('click', () => this.showMainSection());
    this.backFromCouponConfigBtn?.addEventListener('click', () => this.showRewardsSection());
    this.backFromCouponResultBtn?.addEventListener('click', () => this.showRewardsSection());

    // Nouveaux event listeners
    this.backFromYourRewardsBtn?.addEventListener('click', () => this.showMainSection());
    this.backFromWaysToEarnBtn?.addEventListener('click', () => this.showMainSection());
    this.backFromYourActivityBtn?.addEventListener('click', () => this.showMainSection());

    // Coupon events
    this.pointsInput?.addEventListener('input', () => this.updateCouponValue());
    this.redeemCouponBtn?.addEventListener('click', () => this.createCoupon());
    this.copyCouponCodeBtn?.addEventListener('click', () => this.copyCouponCode());
    this.applyCouponBtn?.addEventListener('click', () => this.applyCouponToCart());

    // Parrainage
    this.copyReferralBtn?.addEventListener('click', () => this.copyReferralLink());
    this.shareFacebookBtn?.addEventListener('click', () => this.shareOnFacebook());
    this.shareTwitterBtn?.addEventListener('click', () => this.shareOnTwitter());
    this.shareEmailBtn?.addEventListener('click', () => this.shareByEmail());

    // Gestion du clavier
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.close();
      }
    });

    // Responsive
    window.addEventListener('resize', () => this.handleResize());
  }

  applyCustomStyles() {
    const root = document.documentElement;
    root.style.setProperty('--loyalty-primary', this.options.primaryColor);
    root.style.setProperty('--loyalty-secondary', this.options.secondaryColor);

    // Masquer sur mobile si configuré
    if (!this.options.showOnMobile && window.innerWidth <= 768) {
      this.container.style.display = 'none';
    }
  }

  async loadCustomerData() {
    this.setLoading(true);
    console.log("loadCustomerData ", this.options.customerId);
    try {
      if (!this.options.customerId) {
        this.showGuestState();
        return;
      }

      // Charger les données client via l'App Proxy
      const customerResponse = await fetch(
        `${this.apiBaseUrl}?prepath=customer&shop=${this.options.shop}&logged_in_customer_id=${this.options.customerId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!customerResponse.ok) {
        throw new Error(`HTTP ${customerResponse.status}: ${customerResponse.statusText}`);
      }

      const customerData = await customerResponse.json();
      this.customerData = customerData;

      // Charger les façons de gagner, d'échanger, les produits et les infos du programme en parallèle
      await Promise.all([
        this.loadEarnWays(),
        this.loadRedeemWays(),
        this.loadExchangeableProducts(),
        this.loadProgramInfo()
      ]);

      // Calculer le seuil de la prochaine récompense
      this.calculateNextRewardThreshold();

      // Afficher l'état approprié
      if (customerData.type === 'guest' && customerData.points === 0) {
        console.log("Guest state");
        this.showGuestState();
        console.log("guest state ",     this.guestState )
      } else {
        this.showMemberState();
      }

      this.updatePointsPreview();

    } catch (error) {
      console.error('Erreur lors du chargement des données client:', error);
      this.showErrorState();
    } finally {
      this.setLoading(false);
    }
  }

  async loadEarnWays() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=ways-to-earn&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        this.earnWays = await response.json();
        this.renderEarnWays();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des façons de gagner:', error);
    }
  }

  async loadRedeemWays() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=ways-to-redeem&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        this.redeemWays = await response.json();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des façons d\'échanger:', error);
    }
  }

  async loadProgramInfo() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=program-info&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        this.programInfo = await response.json();

        // Vérifier si le programme est actif et le widget activé
        if (!this.programInfo.isActive || !this.programInfo.widgetEnabled) {
          this.hide();
          return;
        }

        // Appliquer TOUS les paramètres de style personnalisés
        if (this.programInfo.widgetColor) {
          document.documentElement.style.setProperty('--loyalty-primary', this.programInfo.widgetColor);
        }
        if (this.programInfo.widgetSecondaryColor) {
          document.documentElement.style.setProperty('--loyalty-secondary', this.programInfo.widgetSecondaryColor);
        }
        if (this.programInfo.widgetTextColor) {
          document.documentElement.style.setProperty('--loyalty-text-color', this.programInfo.widgetTextColor);
        }

        // Appliquer la couleur de background du widget (toujours blanc par défaut)
        document.documentElement.style.setProperty('--loyalty-widget-background', '#ffffff');

        // Appliquer les paramètres de style avancés
        if (this.programInfo.widgetBorderRadius) {
          const radiusValue = this.programInfo.widgetBorderRadius === 'rounded' ? '16px' :
                             this.programInfo.widgetBorderRadius === 'pill' ? '50px' : '4px';
          document.documentElement.style.setProperty('--loyalty-radius', radiusValue);
        }

        if (this.programInfo.widgetSize) {
          const sizeMultiplier = this.programInfo.widgetSize === 'large' ? '1.2' :
                                this.programInfo.widgetSize === 'small' ? '0.8' : '1';
          document.documentElement.style.setProperty('--loyalty-size-multiplier', sizeMultiplier);
        }

        if (this.programInfo.widgetShadow === false) {
          document.documentElement.style.setProperty('--loyalty-shadow', 'none');
        }

        // Mettre à jour le titre et la description
        this.updateProgramInfo();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des infos du programme:', error);
    }
  }

  async loadExchangeableProducts() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=client-products&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        this.exchangeableProducts = data.products || [];
        this.programInfo = { ...this.programInfo, ...data.programInfo };
      }
    } catch (error) {
      console.error('Erreur lors du chargement des produits échangeables:', error);
      this.exchangeableProducts = [];
    }
  }

  calculateNextRewardThreshold() {
    if (!this.redeemWays.length) {
      this.nextRewardThreshold = 500; // Valeur par défaut
      return;
    }

    const currentPoints = this.customerData?.points || 0;

    // Trouver la prochaine récompense accessible
    const nextReward = this.redeemWays.find(reward => reward.pointsCost > currentPoints);

    if (nextReward) {
      this.nextRewardThreshold = nextReward.pointsCost;
    } else {
      // Si toutes les récompenses sont accessibles, prendre la plus chère
      this.nextRewardThreshold = Math.max(...this.redeemWays.map(r => r.pointsCost));
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
    this.loadingState.style.display = loading ? 'block' : 'none';
    this.guestState.style.display = this.customerData?.type === 'guest' ? 'block' : this.customerData?.type === 'member' ? 'none' : 'block';
    this.memberState.style.display = this.customerData?.type === 'member' ? 'block' : 'none';
    this.errorState.style.display = 'none';
  }

  showGuestState() {
    this.loadingState.style.display = 'none';
    this.guestState.style.display = 'block';
    this.memberState.style.display = 'none';
    this.errorState.style.display = 'none';
    console.log("showGuestState ", this.guestState.style.display);

    // Mettre à jour l'aperçu des points
    this.updatePointsPreview(0);

    // S'assurer que les informations du programme sont affichées
    if (this.programInfo) {
      this.updateProgramInfo();
    }
  }

  showMemberState() {
    this.loadingState.style.display = 'none';
    this.guestState.style.display = 'none';
    this.memberState.style.display = 'block';
    this.errorState.style.display = 'none';

    this.updateCustomerInfo();
    this.showMainSection(); // Afficher la section principale par défaut
  }

  showErrorState() {
    this.loadingState.style.display = 'none';
    this.guestState.style.display = 'none';
    this.memberState.style.display = 'none';
    this.errorState.style.display = 'block';
  }

  updateCustomerInfo() {
    if (!this.customerData) return;

    const { firstName, lastName, email, type, points, ordersCount } = this.customerData;

    // Nom et initiales
    const fullName = firstName && lastName ? `${firstName} ${lastName}` : email || 'Client';
    const initials = firstName && lastName
      ? `${firstName[0]}${lastName[0]}`.toUpperCase()
      : email ? email[0].toUpperCase() : '?';

    this.customerName.textContent = fullName;
    this.customerInitials.textContent = initials;

    // Statut
    this.customerStatus.textContent = type === 'member' ? 'Membre' : 'Invité';
    this.customerStatus.className = `loyalty-status-badge ${type}`;

    // Points et commandes
    this.customerPoints.textContent = points.toLocaleString();
    this.customerOrders.textContent = ordersCount;

    // Progression vers la prochaine récompense
    this.updateProgress();
  }

  updateProgress() {
    const currentPoints = this.customerData?.points || 0;
    const pointsNeeded = Math.max(0, this.nextRewardThreshold - currentPoints);
    const progressPercent = Math.min(100, (currentPoints / this.nextRewardThreshold) * 100);

    if (pointsNeeded > 0) {
      this.pointsNeeded.textContent = `${pointsNeeded} points`;
    } else {
      this.pointsNeeded.textContent = 'Récompense disponible !';
    }

    this.progressFill.style.width = `${progressPercent}%`;
  }

  updatePointsPreview(points = null) {
    const pointsToShow = points !== null ? points : (this.customerData?.points || 0);
    const pointsCount = this.pointsPreview.querySelector('.points-count');
    if (pointsCount) {
      pointsCount.textContent = formatCompactNumber(pointsToShow);
    }
  }

  hide() {
    this.container.style.display = 'none';
  }

  show() {
    this.container.style.display = '';
  }

  updateProgramInfo() {
    if (!this.programInfo) return;
    console.log('program info ', this.programInfo)
    // Mettre à jour le titre du programme
    const titleElement = document.getElementById('loyalty-program-title');
    if (titleElement && this.programInfo.name) {
      titleElement.textContent = this.programInfo.name;
    }

    // Mettre à jour le nom du programme dans l'état guest
    const nameElement = document.getElementById('loyalty-program-name');
    if (nameElement) {
      const programName = this.programInfo.name || 'Programme de fidélité';
      nameElement.textContent = `Rejoignez ${programName} !`;
    }

    // Mettre à jour la description du programme
    const descriptionElement = document.getElementById('loyalty-program-description');
    if (descriptionElement) {
      const description = this.programInfo.description || this.programInfo.welcomeMessage || 'Gagnez des points à chaque achat !';
      descriptionElement.textContent = description;
    }

    // Mettre à jour le nom des points partout
    if (this.programInfo.pointsName) {
      const pointsLabels = document.querySelectorAll('.points-label, .loyalty-stat-label');
      pointsLabels.forEach(label => {
        if (label.textContent && label.textContent.toLowerCase().includes('points')) {
          label.textContent = label.textContent.replace(/points/gi, this.programInfo.pointsName);
        }
      });
    }
  }

  renderEarnWays() {
    if (!this.earnWaysList || !this.earnWays.length) return;

    this.earnWaysList.innerHTML = this.earnWays.map(way => {
      // Formater la description avec la valeur des points
      let pointsDescription = '';
      if (way.earningType === 'increments') {
        pointsDescription = `${way.earningValue} points par €1 dépensé`;
      } else {
        pointsDescription = `${way.earningValue} points fixes`;
      }

      // Choisir l'icône selon le type d'action
      let iconSvg = '';
      switch (way.actionType) {
        case 'order':
          iconSvg = `<path d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H19M7 13v4a2 2 0 002 2h8a2 2 0 002-2v-4m-8 2h.01M15 15h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`;
          break;
        case 'signup':
          iconSvg = `<path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`;
          break;
        default:
          iconSvg = `<path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>`;
      }

      return `
        <div class="loyalty-earn-item">
          <div class="loyalty-earn-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              ${iconSvg}
            </svg>
          </div>
          <div class="loyalty-earn-content">
            <div class="loyalty-earn-title">${way.name}</div>
            <div class="loyalty-earn-description">${pointsDescription}</div>
          </div>
        </div>
      `;
    }).join('');
  }

  // ===== MÉTHODES DE NAVIGATION PRINCIPALES =====

  showMainSection() {
    this.hideAllSections();
    if (this.mainSection) {
      this.mainSection.style.display = 'block';
      this.mainSection.classList.add('loyalty-section-slide-in');
    }
    this.currentView = 'main';
  }

  showRewardsSection() {
    this.hideAllSections();
    if (this.rewardsSection) {
      this.rewardsSection.style.display = 'block';
      this.rewardsSection.classList.add('loyalty-section-slide-in');
      this.renderWaysToRedeem();
    }
    this.currentView = 'rewards';
  }

  showHistorySection() {
    this.hideAllSections();
    if (this.historySection) {
      this.historySection.style.display = 'block';
      this.historySection.classList.add('loyalty-section-slide-in');
      this.renderHistory();
    }
    this.currentView = 'history';
  }

  hideAllSections() {
    // Masquer toutes les sections principales
    const sections = [
      this.mainSection,
      this.rewardsSection,
      this.historySection,
      this.couponConfigSection,
      this.couponResultSection,
      this.yourRewardsSection,
      this.waysToEarnSection,
      this.yourActivitySection
    ];

    sections.forEach(section => {
      if (section) {
        section.style.display = 'none';
        section.classList.remove('loyalty-section-slide-in');
      }
    });
  }

  // ===== NOUVELLES MÉTHODES DE NAVIGATION =====

  showYourRewardsSection() {
    this.hideAllSections();
    if (this.yourRewardsSection) {
      this.yourRewardsSection.style.display = 'block';
      this.yourRewardsSection.classList.add('loyalty-section-slide-in');
      this.renderYourRewards();
    }
    this.currentView = 'your-rewards';
  }

  showWaysToEarnSection() {
    this.hideAllSections();
    if (this.waysToEarnSection) {
      this.waysToEarnSection.style.display = 'block';
      this.waysToEarnSection.classList.add('loyalty-section-slide-in');
      this.renderWaysToEarn();
    }
    this.currentView = 'ways-to-earn';
  }

  showWaysToRedeemSection() {
    this.hideAllSections();
    if (this.rewardsSection) {
      this.rewardsSection.style.display = 'block';
      this.rewardsSection.classList.add('loyalty-section-slide-in');
      this.renderWaysToRedeem();
    }
    this.currentView = 'ways-to-redeem';
  }

  showYourActivitySection() {
    this.hideAllSections();
    if (this.yourActivitySection) {
      this.yourActivitySection.style.display = 'block';
      this.yourActivitySection.classList.add('loyalty-section-slide-in');
      this.renderYourActivity();
    }
    this.currentView = 'your-activity';
  }

  // ===== MÉTHODES DE RENDU =====

  renderWaysToRedeem() {
    if (!this.redeemWays.length) {
      this.rewardsList.innerHTML = `
        <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
          <p>Aucune option de redemption disponible pour le moment.</p>
        </div>
      `;
      return;
    }

    const currentPoints = this.customerData?.points || 0;

    this.rewardsList.innerHTML = this.redeemWays.map(way => {
      const canAfford = way.isConfigurable ?
        currentPoints >= (way.minPoints || 100) :
        currentPoints >= way.pointsCost;

      const statusClass = canAfford ? 'available' : 'unavailable';

      return `
        <div class="loyalty-reward-item ${statusClass}" ${canAfford ? `onclick="loyaltyWidget.handleRedeemClick('${way.id}')"` : ''}>
          <div class="loyalty-reward-icon">💰</div>
          <div class="loyalty-reward-content">
            <div class="loyalty-reward-title">${way.name}</div>
            <div class="loyalty-reward-description">${way.description}</div>
            <div class="loyalty-reward-cost">
              ${way.isConfigurable ?
                `${way.minPoints || 100} Points = €${((way.minPoints || 100) / (this.programInfo?.redemptionRate || 100)).toFixed(2)}` :
                `${way.pointsCost} Points = €${way.redeemValue}`
              }
            </div>
          </div>
          <div class="loyalty-reward-action">
            ${canAfford
              ? '<button class="loyalty-btn loyalty-btn-primary">Redeem</button>'
              : `<span class="loyalty-points-needed">Minimum ${way.isConfigurable ? way.minPoints || 100 : way.pointsCost} points requis</span>`
            }
          </div>
        </div>
      `;
    }).join('');
  }

  renderYourRewards() {
    // Mettre à jour l'en-tête avec les points actuels
    const pointsHeader = document.getElementById('customer-points-header-rewards');
    if (pointsHeader) {
      pointsHeader.textContent = `${formatCompactNumber(this.customerData?.points || 0)} Points`;
    }

    // Simuler des récompenses disponibles
    const availableRewards = [
      {
        id: 1,
        title: '€11 off coupon',
        subtitle: 'Spent 1,100 Points',
        type: 'coupon',
        status: 'available'
      }
    ];

    if (this.yourRewardsList) {
      this.yourRewardsList.innerHTML = availableRewards.map(reward => `
        <div class="loyalty-reward-item available">
          <div class="loyalty-reward-icon">💰</div>
          <div class="loyalty-reward-content">
            <div class="loyalty-reward-title">${reward.title}</div>
            <div class="loyalty-reward-subtitle">${reward.subtitle}</div>
          </div>
          <div class="loyalty-reward-action">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="9,18 15,12 9,6"></polyline>
            </svg>
          </div>
        </div>
      `).join('');
    }

    // Simuler des récompenses passées
    const pastRewards = [
      {
        id: 1,
        title: '€33 off coupon',
        subtitle: 'Used on Jun 17, 2025',
        status: 'used'
      },
      {
        id: 2,
        title: '€39 off coupon',
        subtitle: 'Used on Jun 17, 2025',
        status: 'used'
      }
    ];

    if (this.pastRewardsList) {
      this.pastRewardsList.innerHTML = pastRewards.map(reward => `
        <div class="loyalty-reward-item past">
          <div class="loyalty-reward-icon">💰</div>
          <div class="loyalty-reward-content">
            <div class="loyalty-reward-title">${reward.title}</div>
            <div class="loyalty-reward-subtitle">${reward.subtitle}</div>
          </div>
          <div class="loyalty-reward-status">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="20,6 9,17 4,12"></polyline>
            </svg>
          </div>
        </div>
      `).join('');
    }
  }

  renderWaysToEarn() {
    // Mettre à jour l'en-tête avec les points actuels
    const pointsHeader = document.getElementById('customer-points-header-earn');
    if (pointsHeader) {
      pointsHeader.textContent = `${formatCompactNumber(this.customerData?.points || 0)} Points`;
    }

    if (!this.earnWays.length) {
      this.waysToEarnList.innerHTML = `
        <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
          <p>Aucune façon de gagner des points configurée.</p>
        </div>
      `;
      return;
    }

    this.waysToEarnList.innerHTML = this.earnWays.map(way => `
      <div class="loyalty-earn-item">
        <div class="loyalty-earn-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"></path>
            <circle cx="8.5" cy="7" r="4"></circle>
            <line x1="20" y1="8" x2="20" y2="14"></line>
            <line x1="23" y1="11" x2="17" y2="11"></line>
          </svg>
        </div>
        <div class="loyalty-earn-content">
          <div class="loyalty-earn-title">${way.name}</div>
          <div class="loyalty-earn-description">${way.points} Points for every €1 spent</div>
        </div>
      </div>
    `).join('');
  }

  renderYourActivity() {
    // Mettre à jour l'en-tête avec les points actuels
    const pointsHeader = document.getElementById('customer-points-header-activity');
    if (pointsHeader) {
      pointsHeader.textContent = `${formatCompactNumber(this.customerData?.points || 0)} Points`;
    }

    // Simuler des données d'activité
    const activities = [
      {
        id: 1,
        description: 'Points spent on €11 off coupon',
        points: -1100,
        date: 'Jun 17, 2025',
        type: 'spent'
      },
      {
        id: 2,
        description: 'Placed an order',
        points: 3000,
        date: 'Jun 17, 2025',
        type: 'earned'
      },
      {
        id: 3,
        description: 'Placed an order',
        points: 6765,
        date: 'Jun 17, 2025',
        type: 'earned'
      },
      {
        id: 4,
        description: 'Points spent on €33 off coupon',
        points: -3300,
        date: 'Jun 17, 2025',
        type: 'spent'
      }
    ];

    if (this.activityList) {
      this.activityList.innerHTML = activities.map(activity => `
        <div class="loyalty-activity-item">
          <div class="loyalty-activity-content">
            <div class="loyalty-activity-description">${activity.description}</div>
            <div class="loyalty-activity-points ${activity.type}">
              ${activity.points > 0 ? '+' : ''}${formatCompactNumber(activity.points)} Points
            </div>
          </div>
          <div class="loyalty-activity-date">${activity.date}</div>
        </div>
      `).join('');
    }
  }

  renderHistory() {
    if (!this.historyList) return;

    // Simuler des données d'historique pour l'instant
    const mockHistory = [
      {
        id: 1,
        action: 'Commande #1234',
        points: 50,
        type: 'earned',
        date: new Date(Date.now() - 86400000).toLocaleDateString('fr-FR')
      },
      {
        id: 2,
        action: 'Inscription au programme',
        points: 100,
        type: 'earned',
        date: new Date(Date.now() - 172800000).toLocaleDateString('fr-FR')
      },
      {
        id: 3,
        action: 'Échange produit',
        points: -200,
        type: 'redeemed',
        date: new Date(Date.now() - 259200000).toLocaleDateString('fr-FR')
      }
    ];

    this.historyList.innerHTML = mockHistory.map(item => `
      <div class="loyalty-history-item">
        <div class="loyalty-history-info">
          <div class="loyalty-history-action">${item.action}</div>
          <div class="loyalty-history-date">${item.date}</div>
        </div>
        <div class="loyalty-history-points ${item.type === 'earned' ? 'positive' : 'negative'}">
          ${item.points > 0 ? '+' : ''}${item.points}
        </div>
      </div>
    `).join('');

    if (mockHistory.length === 0) {
      this.historyList.innerHTML = `
        <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
          <p>Aucun historique disponible.</p>
        </div>
      `;
    }
  }

  copyReferralLink() {
    if (this.referralLink) {
      this.referralLink.select();
      this.referralLink.setSelectionRange(0, 99999);
      navigator.clipboard.writeText(this.referralLink.value).then(() => {
        // Feedback visuel
        const originalText = this.copyReferralBtn.innerHTML;
        this.copyReferralBtn.innerHTML = '✓';
        setTimeout(() => {
          this.copyReferralBtn.innerHTML = originalText;
        }, 1000);
      });
    }
  }

  shareOnFacebook() {
    const url = encodeURIComponent(this.referralLink?.value || '');
    const text = encodeURIComponent('Rejoignez-moi sur ce super programme de fidélité !');
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`, '_blank');
  }

  shareOnTwitter() {
    const url = encodeURIComponent(this.referralLink?.value || '');
    const text = encodeURIComponent('Rejoignez-moi sur ce super programme de fidélité !');
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
  }

  shareByEmail() {
    const url = encodeURIComponent(this.referralLink?.value || '');
    const subject = encodeURIComponent('Invitation au programme de fidélité');
    const body = encodeURIComponent(`Salut ! Je t'invite à rejoindre ce super programme de fidélité. Utilise ce lien pour bénéficier d'une réduction : ${this.referralLink?.value || ''}`);
    window.open(`mailto:?subject=${subject}&body=${body}`, '_blank');
  }

  async redeemProduct(productId) {
    if (!this.customerData || !this.options.customerId) {
      alert('Vous devez être connecté pour échanger des points.');
      return;
    }

    const product = this.exchangeableProducts.find(p => p.id === productId);
    if (!product) return;

    if (this.customerData.points < product.pointsCost) {
      alert(`Vous n'avez pas assez de points. Il vous manque ${product.pointsCost - this.customerData.points} points.`);
      return;
    }

    if (!confirm(`Êtes-vous sûr de vouloir échanger ${product.pointsCost} points contre "${product.title}" ?`)) {
      return;
    }

    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=client-redeem`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            shop: this.options.shop,
            customerId: this.options.customerId,
            productId: productId
          })
        }
      );

      const result = await response.json();

      if (response.ok && result.success) {
        // Mettre à jour les points du client
        this.customerData.points = result.remainingPoints;
        this.updateCustomerInfo();
        this.updatePointsPreview();
        this.renderWaysToRedeem(); // Rafraîchir la liste

        alert(`🎉 ${result.message}\nPoints restants: ${result.remainingPoints}`);
      } else {
        alert(`Erreur: ${result.error || 'Impossible d\'échanger le produit'}`);
      }
    } catch (error) {
      console.error('Erreur lors de l\'échange:', error);
      alert('Une erreur est survenue lors de l\'échange. Veuillez réessayer.');
    }
  }

  // ===== NOUVELLES MÉTHODES POUR LES COUPONS =====

  showCouponConfigSection(wayToRedeem) {
    this.currentWayToRedeem = wayToRedeem;
    this.hideAllSections();
    this.couponConfigSection.style.display = 'block';

    // Mettre à jour l'en-tête avec les points actuels
    this.customerPointsHeader.textContent = `${formatCompactNumber(this.customerData.points)} Points`;

    // Configurer les limites d'input
    const minPoints = wayToRedeem.minPoints || 100;
    const maxPoints = Math.min(wayToRedeem.maxPoints || 10000, this.customerData.points);

    this.pointsInput.min = minPoints;
    this.pointsInput.max = maxPoints;
    this.pointsInput.value = minPoints;
    this.pointsInput.step = 100;

    // Mettre à jour la valeur initiale
    this.updateCouponValue();
  }

  showCouponResultSection(couponData) {
    this.hideAllSections();
    this.couponResultSection.style.display = 'block';

    // Mettre à jour l'en-tête avec les nouveaux points
    this.customerPointsResult.textContent = `${formatCompactNumber(this.customerData.points)} Points`;

    // Afficher les détails du coupon
    document.getElementById('generated-coupon-title').textContent = `€${couponData.value} off coupon`;
    document.getElementById('generated-coupon-subtitle').textContent = `Spent ${couponData.pointsSpent} Points`;
    this.generatedCouponCode.value = couponData.code;

    // Stocker le code pour l'application au panier
    this.currentCouponCode = couponData.code;
  }

  // ===== MÉTHODES POUR LES SECTIONS DE COUPONS =====

  handleRedeemClick(wayToRedeemId) {
    const wayToRedeem = this.redeemWays.find(way => way.id === wayToRedeemId);
    if (!wayToRedeem) return;

    if (wayToRedeem.redeemType === 'coupon' && wayToRedeem.isConfigurable) {
      // Coupon configurable - aller à la page de configuration
      this.showCouponConfigSection(wayToRedeem);
    } else if (wayToRedeem.redeemType === 'coupon') {
      // Coupon fixe - créer directement
      this.createFixedCoupon(wayToRedeem);
    } else {
      // Autres types de redemption (produits, etc.)
      this.handleOtherRedemption(wayToRedeem);
    }
  }

  async updateCouponValue() {
    const points = parseInt(this.pointsInput.value) || 0;
    if (points <= 0) {
      this.couponValueDisplay.textContent = '€0';
      return;
    }

    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=coupon-value&shop=${this.options.shop}&points=${points}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        this.couponValueDisplay.textContent = data.formatted;

        // Mettre à jour le titre et sous-titre
        document.getElementById('coupon-config-title').textContent = `${data.formatted} off coupon`;
        document.getElementById('coupon-config-subtitle').textContent = `Spent ${formatCompactNumber(points)} Points`;
      }
    } catch (error) {
      console.error('Erreur lors du calcul de la valeur du coupon:', error);
    }
  }

  async createCoupon() {
    const pointsToSpend = parseInt(this.pointsInput.value) || 0;

    if (pointsToSpend <= 0) {
      alert('Veuillez entrer un nombre de points valide.');
      return;
    }

    if (pointsToSpend > this.customerData.points) {
      alert('Vous n\'avez pas assez de points.');
      return;
    }

    try {
      this.redeemCouponBtn.disabled = true;
      this.redeemCouponBtn.textContent = 'Création...';

      const response = await fetch(
        `${this.apiBaseUrl}?prepath=create-coupon`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            shop: this.options.shop,
            customerId: this.options.customerId,
            pointsToSpend: pointsToSpend.toString(),
            wayToRedeemId: this.currentWayToRedeem?.id || ''
          })
        }
      );

      const result = await response.json();

      if (response.ok && result.success) {
        // Mettre à jour les points du client
        this.customerData.points -= pointsToSpend;
        this.updateCustomerInfo();
        this.updatePointsPreview();

        // Afficher la page de résultat
        this.showCouponResultSection(result.coupon);
      } else {
        alert(`Erreur: ${result.error || 'Impossible de créer le coupon'}`);
      }
    } catch (error) {
      console.error('Erreur lors de la création du coupon:', error);
      alert('Une erreur est survenue lors de la création du coupon. Veuillez réessayer.');
    } finally {
      this.redeemCouponBtn.disabled = false;
      this.redeemCouponBtn.textContent = 'Redeem';
    }
  }

  async createFixedCoupon(wayToRedeem) {
    if (this.customerData.points < wayToRedeem.pointsCost) {
      alert(`Vous n'avez pas assez de points. Il vous manque ${wayToRedeem.pointsCost - this.customerData.points} points.`);
      return;
    }

    if (!confirm(`Êtes-vous sûr de vouloir échanger ${wayToRedeem.pointsCost} points contre un coupon de €${wayToRedeem.redeemValue} ?`)) {
      return;
    }

    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=create-coupon`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            shop: this.options.shop,
            customerId: this.options.customerId,
            pointsToSpend: wayToRedeem.pointsCost.toString(),
            wayToRedeemId: wayToRedeem.id
          })
        }
      );

      const result = await response.json();

      if (response.ok && result.success) {
        // Mettre à jour les points du client
        this.customerData.points -= wayToRedeem.pointsCost;
        this.updateCustomerInfo();
        this.updatePointsPreview();

        // Afficher la page de résultat
        this.showCouponResultSection(result.coupon);
      } else {
        alert(`Erreur: ${result.error || 'Impossible de créer le coupon'}`);
      }
    } catch (error) {
      console.error('Erreur lors de la création du coupon:', error);
      alert('Une erreur est survenue lors de la création du coupon. Veuillez réessayer.');
    }
  }

  copyCouponCode() {
    if (this.generatedCouponCode) {
      this.generatedCouponCode.select();
      this.generatedCouponCode.setSelectionRange(0, 99999);
      navigator.clipboard.writeText(this.generatedCouponCode.value).then(() => {
        // Feedback visuel
        const originalText = this.copyCouponCodeBtn.innerHTML;
        this.copyCouponCodeBtn.innerHTML = '✓';
        this.copyCouponCodeBtn.style.background = '#4CAF50';
        setTimeout(() => {
          this.copyCouponCodeBtn.innerHTML = originalText;
          this.copyCouponCodeBtn.style.background = '';
        }, 1000);
      });
    }
  }

  async applyCouponToCart() {
    if (!this.currentCouponCode) {
      alert('Aucun code de coupon disponible.');
      return;
    }

    try {
      // Utiliser l'API Shopify pour appliquer le code de réduction
      const response = await fetch('/cart/update.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          attributes: {
            'discount_code': this.currentCouponCode
          }
        })
      });

      if (response.ok) {
        // Rediriger vers le panier avec le code appliqué
        window.location.href = `/cart?discount=${this.currentCouponCode}`;
      } else {
        // Fallback : rediriger vers le checkout avec le code
        window.location.href = `/checkout?discount=${this.currentCouponCode}`;
      }
    } catch (error) {
      console.error('Erreur lors de l\'application du coupon:', error);
      // Fallback : rediriger vers le checkout avec le code
      window.location.href = `/checkout?discount=${this.currentCouponCode}`;
    }
  }

  handleOtherRedemption(wayToRedeem) {
    // Pour les autres types de redemption (produits, etc.)
    // Cette méthode peut être étendue selon les besoins
    alert(`Redemption de type "${wayToRedeem.redeemType}" pas encore implémentée.`);
  }

  open() {
    if (this.isOpen) return;

    this.isOpen = true;
    this.panel.classList.add('active');
    this.overlay.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Animation du bouton
    this.trigger.style.transform = 'scale(0.9)';
    setTimeout(() => {
      this.trigger.style.transform = '';
    }, 150);
  }

  close() {
    if (!this.isOpen) return;

    this.isOpen = false;
    this.panel.classList.remove('active');
    this.overlay.classList.remove('active');
    document.body.style.overflow = '';
  }

  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  openRewards() {
    this.currentView = 'rewards';
    this.renderRewardsView();
  }

  openHistory() {
    this.currentView = 'history';
    this.renderHistoryView();
  }

  backToMain() {
    this.currentView = 'main';
    this.renderMainView();
  }

  renderMainView() {
    // Afficher le contenu principal et masquer les autres vues
    const mainContent = this.panel.querySelector('.loyalty-main-content');
    const rewardsContent = this.panel.querySelector('.loyalty-rewards-content');
    const historyContent = this.panel.querySelector('.loyalty-history-content');

    if (mainContent) mainContent.style.display = 'block';
    if (rewardsContent) rewardsContent.style.display = 'none';
    if (historyContent) historyContent.style.display = 'none';
  }

  renderRewardsView() {
    // Masquer le contenu principal et afficher les récompenses
    const mainContent = this.panel.querySelector('.loyalty-main-content');
    const rewardsContent = this.panel.querySelector('.loyalty-rewards-content');
    const historyContent = this.panel.querySelector('.loyalty-history-content');

    if (mainContent) mainContent.style.display = 'none';
    if (historyContent) historyContent.style.display = 'none';

    if (!rewardsContent) {
      this.createRewardsView();
    } else {
      rewardsContent.style.display = 'block';
    }
  }

  renderHistoryView() {
    // Masquer le contenu principal et afficher l'historique
    const mainContent = this.panel.querySelector('.loyalty-main-content');
    const rewardsContent = this.panel.querySelector('.loyalty-rewards-content');
    const historyContent = this.panel.querySelector('.loyalty-history-content');

    if (mainContent) mainContent.style.display = 'none';
    if (rewardsContent) rewardsContent.style.display = 'none';

    if (!historyContent) {
      this.createHistoryView();
    } else {
      historyContent.style.display = 'block';
    }
  }

  createRewardsView() {
    const rewardsContent = document.createElement('div');
    rewardsContent.className = 'loyalty-rewards-content';
    rewardsContent.innerHTML = `
      <div class="loyalty-view-header">
        <button class="loyalty-back-btn" onclick="loyaltyWidget.backToMain()">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          Retour
        </button>
        <h3>Récompenses disponibles</h3>
      </div>
      <div class="loyalty-rewards-list">
        ${this.renderRewardsList()}
      </div>
    `;

    this.panel.querySelector('.loyalty-content').appendChild(rewardsContent);
  }

  createHistoryView() {
    const historyContent = document.createElement('div');
    historyContent.className = 'loyalty-history-content';
    historyContent.innerHTML = `
      <div class="loyalty-view-header">
        <button class="loyalty-back-btn" onclick="loyaltyWidget.backToMain()">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          Retour
        </button>
        <h3>Historique des points</h3>
      </div>
      <div class="loyalty-history-list">
        ${this.renderHistoryList()}
      </div>
    `;

    this.panel.querySelector('.loyalty-content').appendChild(historyContent);
  }

  renderRewardsList() {
    if (!this.redeemWays.length) {
      return '<div class="loyalty-empty-state">Aucune récompense disponible pour le moment.</div>';
    }

    const currentPoints = this.customerData?.points || 0;

    return this.redeemWays.map(reward => {
      const canAfford = currentPoints >= reward.pointsCost;
      const statusClass = canAfford ? 'available' : 'unavailable';

      return `
        <div class="loyalty-reward-item ${statusClass}">
          <div class="loyalty-reward-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
            </svg>
          </div>
          <div class="loyalty-reward-content">
            <div class="loyalty-reward-title">${reward.name}</div>
            <div class="loyalty-reward-description">${reward.description}</div>
            <div class="loyalty-reward-cost">${formatCompactNumber(reward.pointsCost)} points</div>
          </div>
          <div class="loyalty-reward-action">
            ${canAfford
              ? '<button class="loyalty-btn loyalty-btn-primary loyalty-btn-sm">Échanger</button>'
              : `<span class="loyalty-points-needed">Il vous manque ${formatCompactNumber(reward.pointsCost - currentPoints)} points</span>`
            }
          </div>
        </div>
      `;
    }).join('');
  }

  renderHistoryList() {
    const history = this.customerData?.recentHistory || [];

    if (!history.length) {
      return '<div class="loyalty-empty-state">Aucun historique disponible.</div>';
    }

    return history.map(item => {
      const isPositive = item.points > 0;
      const pointsClass = isPositive ? 'positive' : 'negative';
      const pointsPrefix = isPositive ? '+' : '';

      return `
        <div class="loyalty-history-item">
          <div class="loyalty-history-icon ${pointsClass}">
            ${isPositive
              ? '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 5V19M5 12L19 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
              : '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
            }
          </div>
          <div class="loyalty-history-content">
            <div class="loyalty-history-description">${item.description}</div>
            <div class="loyalty-history-date">${new Date(item.timestamp).toLocaleDateString('fr-FR')}</div>
          </div>
          <div class="loyalty-history-points ${pointsClass}">
            ${pointsPrefix}${formatCompactNumber(item.points)}
          </div>
        </div>
      `;
    }).join('');
  }

  async signupToLoyalty() {
    if (!this.options.customerId) {
      console.error('Customer ID required for signup');
      return;
    }

    try {
      const response = await fetch(`${this.apiBaseUrl}?prepath=signup&shop=${this.options.shop}&logged_in_customer_id=${this.options.customerId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const result = await response.json();

        // Mettre à jour les données client
        this.customerData = result.customer;
        this.showMemberState();
        this.updatePointsPreview();

        // Afficher un message de succès
        this.showSuccessMessage(result.message);
      } else {
        throw new Error('Erreur lors de l\'inscription');
      }
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error);
      this.showErrorMessage('Impossible de vous inscrire au programme. Veuillez réessayer.');
    }
  }

  showSuccessMessage(message) {
    // Créer une notification temporaire
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000000;
      font-family: inherit;
      font-size: 14px;
      max-width: 300px;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Supprimer après 5 secondes
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  showErrorMessage(message) {
    // Créer une notification d'erreur temporaire
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #f44336;
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000000;
      font-family: inherit;
      font-size: 14px;
      max-width: 300px;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Supprimer après 5 secondes
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  reload() {
    this.loadCustomerData();
  }

  handleResize() {
    if (!this.options.showOnMobile && window.innerWidth <= 768) {
      this.container.style.display = 'none';
    } else {
      this.container.style.display = '';
    }
  }

  hasSeenWidget() {
    return localStorage.getItem('loyalty-widget-seen') === 'true';
  }

  markWidgetSeen() {
    localStorage.setItem('loyalty-widget-seen', 'true');
  }

  // API publique
  updateCustomer(customerId) {
    this.options.customerId = customerId;
    this.loadCustomerData();
  }

  updatePoints(points) {
    if (this.customerData) {
      this.customerData.points = points;
      this.updatePointsPreview();
      this.updateProgress();
    }
  }
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', function() {
  // Vérifier si le widget existe
  if (document.getElementById('loyalty-widget')) {
    // Récupérer les options depuis la configuration Liquid
    const config = window.loyaltyWidgetConfig || {};

    const options = {
      shop: config.shop || '',
      customerId: config.customerId || null,
      position: config.position || 'bottom-right',
      primaryColor: config.primaryColor || '#2E7D32',
      secondaryColor: config.secondaryColor || '#4CAF50',
      showOnMobile: config.showOnMobile !== false,
      autoOpen: config.autoOpen || false
    };

    // Créer l'instance globale
    window.loyaltyWidget = new LoyaltyWidget(options);
  }
});
