import { json, type ActionFunctionArgs } from "@remix-run/node";
import { getCustomerByShopifyId, updateCustomerPoints } from "../models/Customer.server";
import { getExchangeableProducts } from "../models/ExchangeableProducts.server";
import { getSiteSettings } from "../models/SiteSettings.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const formData = await request.formData();
    const shop = formData.get('shop') as string;
    const customerId = formData.get('customerId') as string;
    const productId = formData.get('productId') as string;

    if (!shop || !customerId || !productId) {
      return json({ error: "Paramètres manquants" }, { status: 400 });
    }

    // Récupérer les données du client
    const customerData = await getCustomerByShopifyId(customerId, shop);
    if (!customerData) {
      return json({ error: "Client non trouvé" }, { status: 404 });
    }

    // Récupérer le produit et les paramètres
    const [exchangeableProducts, settings] = await Promise.all([
      getExchangeableProducts(shop),
      getSiteSettings(shop)
    ]);

    const product = exchangeableProducts.find(p => p.id === productId);
    if (!product) {
      return json({ error: "Produit non trouvé" }, { status: 404 });
    }

    // Calculer le coût en points
    const pointsCost = settings?.redemptionRate
      ? Math.ceil(parseFloat(product.price) / settings.redemptionRate)
      : product.pointsCost;

    // Vérifier si le client a assez de points
    if (customerData.points < pointsCost) {
      return json({
        error: "Points insuffisants",
        required: pointsCost,
        available: customerData.points
      }, { status: 400 });
    }

    // Déduire les points
    const newPoints = customerData.points - pointsCost;
    await updateCustomerPoints(customerId, shop, newPoints);

    // TODO: Ici vous pourriez ajouter la logique pour créer un bon de réduction Shopify
    // ou envoyer un email au client avec les instructions

    return json({
      success: true,
      message: `Produit "${product.title}" échangé avec succès !`,
      pointsUsed: pointsCost,
      remainingPoints: newPoints,
      product: {
        title: product.title,
        image: product.image
      }
    });

  } catch (error) {
    console.error("Error redeeming product:", error);
    return json({ error: "Erreur lors de l'échange" }, { status: 500 });
  }
};
