/**
 * Widget de fidélité - Interface client
 * Version: 1.0.0
 */

/**
 * Formate un nombre pour l'affichage compact (1k, 1.2k, 1M, etc.)
 */
function formatCompactNumber(num) {
  if (num < 1000) return num.toString();

  if (num < 1000000) {
    const k = num / 1000;
    return k % 1 === 0 ? `${k}k` : `${k.toFixed(1)}k`;
  }

  if (num < 1000000000) {
    const m = num / 1000000;
    return m % 1 === 0 ? `${m}M` : `${m.toFixed(1)}M`;
  }

  const b = num / 1000000000;
  return b % 1 === 0 ? `${b}B` : `${b.toFixed(1)}B`;
}

class LoyaltyWidget {
  constructor(options = {}) {
    this.options = {
      shop: options.shop || '',
      customerId: options.customerId || null,
      position: options.position || 'bottom-right',
      primaryColor: options.primaryColor || '#2E7D32',
      secondaryColor: options.secondaryColor || '#4CAF50',
      autoOpen: options.autoOpen || false,
      showOnMobile: options.showOnMobile !== false,
      ...options
    };

    // URL de l'App Proxy Shopify (stable)
    this.apiBaseUrl = `/apps/proxy`;

    this.isOpen = false;
    this.isLoading = false;
    this.customerData = null;
    this.earnWays = [];
    this.redeemWays = [];
    this.nextRewardThreshold = 500; // Par défaut, sera calculé dynamiquement
    this.programInfo = null;
    this.currentView = 'main'; // 'main', 'rewards', 'history'

    this.init();
  }

  init() {
    this.createElements();
    this.bindEvents();
    this.applyCustomStyles();
    this.loadCustomerData();

    // Auto-ouverture si configuré
    if (this.options.autoOpen && !this.hasSeenWidget()) {
      setTimeout(() => this.open(), 2000);
      this.markWidgetSeen();
    }

  }

  createElements() {
    this.container = document.getElementById('loyalty-widget');
    this.trigger = document.getElementById('loyalty-trigger');
    this.panel = document.getElementById('loyalty-panel');
    this.overlay = document.getElementById('loyalty-overlay');
    this.closeBtn = document.getElementById('loyalty-close');

    // Éléments de contenu
    this.pointsPreview = document.getElementById('points-preview');
    this.loadingState = document.getElementById('loyalty-loading');
    this.guestState = document.getElementById('loyalty-guest');
    this.memberState = document.getElementById('loyalty-member');
    this.errorState = document.getElementById('loyalty-error');

    // Éléments de données client
    this.customerInitials = document.getElementById('customer-initials');
    this.customerName = document.getElementById('customer-name');
    this.customerStatus = document.getElementById('customer-status');
    this.customerPoints = document.getElementById('customer-points');
    this.customerOrders = document.getElementById('customer-orders');
    this.pointsNeeded = document.getElementById('points-needed');
    this.progressFill = document.getElementById('progress-fill');
    this.earnWaysList = document.getElementById('earn-ways');

    // Boutons d'action
    this.viewRewardsBtn = document.getElementById('view-rewards');
    this.viewHistoryBtn = document.getElementById('view-history');
    this.backFromRewardsBtn = document.getElementById('back-from-rewards');
    this.backFromHistoryBtn = document.getElementById('back-from-history');

    // Sections
    this.mainSection = document.getElementById('loyalty-main-section');
    this.rewardsSection = document.getElementById('loyalty-rewards-section');
    this.historySection = document.getElementById('loyalty-history-section');
    this.rewardsList = document.getElementById('rewards-list');
    this.historyList = document.getElementById('history-list');

    // Éléments de parrainage
    this.referralLink = document.getElementById('referral-link');
    this.copyReferralBtn = document.getElementById('copy-referral-link');
    this.shareFacebookBtn = document.getElementById('share-facebook');
    this.shareTwitterBtn = document.getElementById('share-twitter');
    this.shareEmailBtn = document.getElementById('share-email');
    this.referralCount = document.getElementById('referral-count');

    // Appliquer la position
    this.container.classList.add(`position-${this.options.position}`);
  }

  bindEvents() {
    // Ouverture/fermeture du widget
    this.trigger.addEventListener('click', () => this.toggle());
    this.closeBtn.addEventListener('click', () => this.close());
    this.overlay.addEventListener('click', () => this.close());

    // Actions
    this.viewRewardsBtn?.addEventListener('click', () => this.showRewardsSection());
    this.viewHistoryBtn?.addEventListener('click', () => this.showHistorySection());
    this.backFromRewardsBtn?.addEventListener('click', () => this.showMainSection());
    this.backFromHistoryBtn?.addEventListener('click', () => this.showMainSection());

    // Parrainage
    this.copyReferralBtn?.addEventListener('click', () => this.copyReferralLink());
    this.shareFacebookBtn?.addEventListener('click', () => this.shareOnFacebook());
    this.shareTwitterBtn?.addEventListener('click', () => this.shareOnTwitter());
    this.shareEmailBtn?.addEventListener('click', () => this.shareByEmail());

    // Gestion du clavier
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.close();
      }
    });

    // Responsive
    window.addEventListener('resize', () => this.handleResize());
  }

  applyCustomStyles() {
    const root = document.documentElement;
    root.style.setProperty('--loyalty-primary', this.options.primaryColor);
    root.style.setProperty('--loyalty-secondary', this.options.secondaryColor);

    // Masquer sur mobile si configuré
    if (!this.options.showOnMobile && window.innerWidth <= 768) {
      this.container.style.display = 'none';
    }
  }

  async loadCustomerData() {
    this.setLoading(true);
    console.log("loadCustomerData ", this.options.customerId);
    try {
      if (!this.options.customerId) {
        this.showGuestState();
        return;
      }

      // Charger les données client via l'App Proxy
      const customerResponse = await fetch(
        `${this.apiBaseUrl}?prepath=customer&shop=${this.options.shop}&logged_in_customer_id=${this.options.customerId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!customerResponse.ok) {
        throw new Error(`HTTP ${customerResponse.status}: ${customerResponse.statusText}`);
      }

      const customerData = await customerResponse.json();
      this.customerData = customerData;

      // Charger les façons de gagner, d'échanger, les produits et les infos du programme en parallèle
      await Promise.all([
        this.loadEarnWays(),
        this.loadRedeemWays(),
        this.loadExchangeableProducts(),
        this.loadProgramInfo()
      ]);

      // Calculer le seuil de la prochaine récompense
      this.calculateNextRewardThreshold();

      // Afficher l'état approprié
      if (customerData.type === 'guest' && customerData.points === 0) {
        console.log("Guest state");
        this.showGuestState();
        console.log("guest state ",     this.guestState )
      } else {
        this.showMemberState();
      }

      this.updatePointsPreview();

    } catch (error) {
      console.error('Erreur lors du chargement des données client:', error);
      this.showErrorState();
    } finally {
      this.setLoading(false);
    }
  }

  async loadEarnWays() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=ways-to-earn&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        this.earnWays = await response.json();
        this.renderEarnWays();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des façons de gagner:', error);
    }
  }

  async loadRedeemWays() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=ways-to-redeem&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        this.redeemWays = await response.json();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des façons d\'échanger:', error);
    }
  }

  async loadProgramInfo() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=program-info&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        this.programInfo = await response.json();

        // Vérifier si le programme est actif et le widget activé
        if (!this.programInfo.isActive || !this.programInfo.widgetEnabled) {
          this.hide();
          return;
        }

        // Appliquer TOUS les paramètres de style personnalisés
        if (this.programInfo.widgetColor) {
          document.documentElement.style.setProperty('--loyalty-primary', this.programInfo.widgetColor);
        }
        if (this.programInfo.widgetSecondaryColor) {
          document.documentElement.style.setProperty('--loyalty-secondary', this.programInfo.widgetSecondaryColor);
        }
        if (this.programInfo.widgetTextColor) {
          document.documentElement.style.setProperty('--loyalty-text-color', this.programInfo.widgetTextColor);
        }

        // Appliquer la couleur de background du widget (toujours blanc par défaut)
        document.documentElement.style.setProperty('--loyalty-widget-background', '#ffffff');

        // Appliquer les paramètres de style avancés
        if (this.programInfo.widgetBorderRadius) {
          const radiusValue = this.programInfo.widgetBorderRadius === 'rounded' ? '16px' :
                             this.programInfo.widgetBorderRadius === 'pill' ? '50px' : '4px';
          document.documentElement.style.setProperty('--loyalty-radius', radiusValue);
        }

        if (this.programInfo.widgetSize) {
          const sizeMultiplier = this.programInfo.widgetSize === 'large' ? '1.2' :
                                this.programInfo.widgetSize === 'small' ? '0.8' : '1';
          document.documentElement.style.setProperty('--loyalty-size-multiplier', sizeMultiplier);
        }

        if (this.programInfo.widgetShadow === false) {
          document.documentElement.style.setProperty('--loyalty-shadow', 'none');
        }

        // Mettre à jour le titre et la description
        this.updateProgramInfo();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des infos du programme:', error);
    }
  }

  async loadExchangeableProducts() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=client-products&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        this.exchangeableProducts = data.products || [];
        this.programInfo = { ...this.programInfo, ...data.programInfo };
      }
    } catch (error) {
      console.error('Erreur lors du chargement des produits échangeables:', error);
      this.exchangeableProducts = [];
    }
  }

  calculateNextRewardThreshold() {
    if (!this.redeemWays.length) {
      this.nextRewardThreshold = 500; // Valeur par défaut
      return;
    }

    const currentPoints = this.customerData?.points || 0;

    // Trouver la prochaine récompense accessible
    const nextReward = this.redeemWays.find(reward => reward.pointsCost > currentPoints);

    if (nextReward) {
      this.nextRewardThreshold = nextReward.pointsCost;
    } else {
      // Si toutes les récompenses sont accessibles, prendre la plus chère
      this.nextRewardThreshold = Math.max(...this.redeemWays.map(r => r.pointsCost));
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
    this.loadingState.style.display = loading ? 'block' : 'none';
    this.guestState.style.display = this.customerData?.type === 'guest' ? 'block' : this.customerData?.type === 'member' ? 'none' : 'block';
    this.memberState.style.display = this.customerData?.type === 'member' ? 'block' : 'none';
    this.errorState.style.display = 'none';
  }

  showGuestState() {
    this.loadingState.style.display = 'none';
    this.guestState.style.display = 'block';
    this.memberState.style.display = 'none';
    this.errorState.style.display = 'none';
    console.log("showGuestState ", this.guestState.style.display);

    // Mettre à jour l'aperçu des points
    this.updatePointsPreview(0);

    // S'assurer que les informations du programme sont affichées
    if (this.programInfo) {
      this.updateProgramInfo();
    }
  }

  showMemberState() {
    this.loadingState.style.display = 'none';
    this.guestState.style.display = 'none';
    this.memberState.style.display = 'block';
    this.errorState.style.display = 'none';

    this.updateCustomerInfo();
    this.showMainSection(); // Afficher la section principale par défaut
  }

  showErrorState() {
    this.loadingState.style.display = 'none';
    this.guestState.style.display = 'none';
    this.memberState.style.display = 'none';
    this.errorState.style.display = 'block';
  }

  updateCustomerInfo() {
    if (!this.customerData) return;

    const { firstName, lastName, email, type, points, ordersCount } = this.customerData;

    // Nom et initiales
    const fullName = firstName && lastName ? `${firstName} ${lastName}` : email || 'Client';
    const initials = firstName && lastName
      ? `${firstName[0]}${lastName[0]}`.toUpperCase()
      : email ? email[0].toUpperCase() : '?';

    this.customerName.textContent = fullName;
    this.customerInitials.textContent = initials;

    // Statut
    this.customerStatus.textContent = type === 'member' ? 'Membre' : 'Invité';
    this.customerStatus.className = `loyalty-status-badge ${type}`;

    // Points et commandes
    this.customerPoints.textContent = points.toLocaleString();
    this.customerOrders.textContent = ordersCount;

    // Progression vers la prochaine récompense
    this.updateProgress();
  }

  updateProgress() {
    const currentPoints = this.customerData?.points || 0;
    const pointsNeeded = Math.max(0, this.nextRewardThreshold - currentPoints);
    const progressPercent = Math.min(100, (currentPoints / this.nextRewardThreshold) * 100);

    if (pointsNeeded > 0) {
      this.pointsNeeded.textContent = `${pointsNeeded} points`;
    } else {
      this.pointsNeeded.textContent = 'Récompense disponible !';
    }

    this.progressFill.style.width = `${progressPercent}%`;
  }

  updatePointsPreview(points = null) {
    const pointsToShow = points !== null ? points : (this.customerData?.points || 0);
    const pointsCount = this.pointsPreview.querySelector('.points-count');
    if (pointsCount) {
      pointsCount.textContent = formatCompactNumber(pointsToShow);
    }
  }

  hide() {
    this.container.style.display = 'none';
  }

  show() {
    this.container.style.display = '';
  }

  updateProgramInfo() {
    if (!this.programInfo) return;
    console.log('program info ', this.programInfo)
    // Mettre à jour le titre du programme
    const titleElement = document.getElementById('loyalty-program-title');
    if (titleElement && this.programInfo.name) {
      titleElement.textContent = this.programInfo.name;
    }

    // Mettre à jour le nom du programme dans l'état guest
    const nameElement = document.getElementById('loyalty-program-name');
    if (nameElement) {
      const programName = this.programInfo.name || 'Programme de fidélité';
      nameElement.textContent = `Rejoignez ${programName} !`;
    }

    // Mettre à jour la description du programme
    const descriptionElement = document.getElementById('loyalty-program-description');
    if (descriptionElement) {
      const description = this.programInfo.description || this.programInfo.welcomeMessage || 'Gagnez des points à chaque achat !';
      descriptionElement.textContent = description;
    }

    // Mettre à jour le nom des points partout
    if (this.programInfo.pointsName) {
      const pointsLabels = document.querySelectorAll('.points-label, .loyalty-stat-label');
      pointsLabels.forEach(label => {
        if (label.textContent && label.textContent.toLowerCase().includes('points')) {
          label.textContent = label.textContent.replace(/points/gi, this.programInfo.pointsName);
        }
      });
    }
  }

  renderEarnWays() {
    if (!this.earnWaysList || !this.earnWays.length) return;

    this.earnWaysList.innerHTML = this.earnWays.map(way => {
      // Formater la description avec la valeur des points
      let pointsDescription = '';
      if (way.earningType === 'increments') {
        pointsDescription = `${way.earningValue} points par €1 dépensé`;
      } else {
        pointsDescription = `${way.earningValue} points fixes`;
      }

      // Choisir l'icône selon le type d'action
      let iconSvg = '';
      switch (way.actionType) {
        case 'order':
          iconSvg = `<path d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H19M7 13v4a2 2 0 002 2h8a2 2 0 002-2v-4m-8 2h.01M15 15h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`;
          break;
        case 'signup':
          iconSvg = `<path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`;
          break;
        default:
          iconSvg = `<path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>`;
      }

      return `
        <div class="loyalty-earn-item">
          <div class="loyalty-earn-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              ${iconSvg}
            </svg>
          </div>
          <div class="loyalty-earn-content">
            <div class="loyalty-earn-title">${way.name}</div>
            <div class="loyalty-earn-description">${pointsDescription}</div>
          </div>
        </div>
      `;
    }).join('');
  }

  showRewardsSection() {
    this.hideAllSections();
    if (this.rewardsSection) {
      this.rewardsSection.style.display = 'block';
      this.rewardsSection.classList.add('loyalty-section-slide-in');
      this.renderExchangeableProducts();
    }
  }

  showHistorySection() {
    this.hideAllSections();
    if (this.historySection) {
      this.historySection.style.display = 'block';
      this.historySection.classList.add('loyalty-section-slide-in');
      this.renderHistory();
    }
  }

  showMainSection() {
    this.hideAllSections();
    if (this.mainSection) {
      this.mainSection.style.display = 'block';
      this.mainSection.classList.add('loyalty-section-slide-in');
    }
  }

  hideAllSections() {
    // Masquer toutes les sections
    if (this.mainSection) this.mainSection.style.display = 'none';
    if (this.rewardsSection) this.rewardsSection.style.display = 'none';
    if (this.historySection) this.historySection.style.display = 'none';

    // Supprimer les classes d'animation
    [this.mainSection, this.rewardsSection, this.historySection].forEach(section => {
      if (section) section.classList.remove('loyalty-section-slide-in');
    });
  }

  renderExchangeableProducts() {
    if (!this.rewardsList || !this.exchangeableProducts) return;

    const currentPoints = this.customerData?.points || 0;

    this.rewardsList.innerHTML = this.exchangeableProducts.map(product => {
      const canAfford = currentPoints >= product.pointsCost;
      const buttonText = canAfford ? 'Échanger' : `${product.pointsCost - currentPoints} points manquants`;

      return `
        <div class="loyalty-reward-item ${!canAfford ? 'unavailable' : ''}" data-product-id="${product.id}">
          ${product.image ?
            `<img src="${product.image}" alt="${product.title}" class="loyalty-reward-image" />` :
            `<div class="loyalty-reward-image"></div>`
          }
          <div class="loyalty-reward-content">
            <div class="loyalty-reward-title">${product.title}</div>
            <div class="loyalty-reward-cost">${product.pointsCost} ${this.programInfo?.pointsName || 'points'}</div>
          </div>
          <div class="loyalty-reward-action">
            <button
              class="loyalty-reward-btn"
              ${!canAfford ? 'disabled' : ''}
              onclick="loyaltyWidget.redeemProduct('${product.id}')"
            >
              ${buttonText}
            </button>
          </div>
        </div>
      `;
    }).join('');

    if (this.exchangeableProducts.length === 0) {
      this.rewardsList.innerHTML = `
        <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
          <p>Aucun produit échangeable disponible pour le moment.</p>
        </div>
      `;
    }
  }

  renderHistory() {
    if (!this.historyList) return;

    // Simuler des données d'historique pour l'instant
    const mockHistory = [
      {
        id: 1,
        action: 'Commande #1234',
        points: 50,
        type: 'earned',
        date: new Date(Date.now() - 86400000).toLocaleDateString('fr-FR')
      },
      {
        id: 2,
        action: 'Inscription au programme',
        points: 100,
        type: 'earned',
        date: new Date(Date.now() - 172800000).toLocaleDateString('fr-FR')
      },
      {
        id: 3,
        action: 'Échange produit',
        points: -200,
        type: 'redeemed',
        date: new Date(Date.now() - 259200000).toLocaleDateString('fr-FR')
      }
    ];

    this.historyList.innerHTML = mockHistory.map(item => `
      <div class="loyalty-history-item">
        <div class="loyalty-history-info">
          <div class="loyalty-history-action">${item.action}</div>
          <div class="loyalty-history-date">${item.date}</div>
        </div>
        <div class="loyalty-history-points ${item.type === 'earned' ? 'positive' : 'negative'}">
          ${item.points > 0 ? '+' : ''}${item.points}
        </div>
      </div>
    `).join('');

    if (mockHistory.length === 0) {
      this.historyList.innerHTML = `
        <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
          <p>Aucun historique disponible.</p>
        </div>
      `;
    }
  }

  copyReferralLink() {
    if (this.referralLink) {
      this.referralLink.select();
      this.referralLink.setSelectionRange(0, 99999);
      navigator.clipboard.writeText(this.referralLink.value).then(() => {
        // Feedback visuel
        const originalText = this.copyReferralBtn.innerHTML;
        this.copyReferralBtn.innerHTML = '✓';
        setTimeout(() => {
          this.copyReferralBtn.innerHTML = originalText;
        }, 1000);
      });
    }
  }

  shareOnFacebook() {
    const url = encodeURIComponent(this.referralLink?.value || '');
    const text = encodeURIComponent('Rejoignez-moi sur ce super programme de fidélité !');
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`, '_blank');
  }

  shareOnTwitter() {
    const url = encodeURIComponent(this.referralLink?.value || '');
    const text = encodeURIComponent('Rejoignez-moi sur ce super programme de fidélité !');
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
  }

  shareByEmail() {
    const url = encodeURIComponent(this.referralLink?.value || '');
    const subject = encodeURIComponent('Invitation au programme de fidélité');
    const body = encodeURIComponent(`Salut ! Je t'invite à rejoindre ce super programme de fidélité. Utilise ce lien pour bénéficier d'une réduction : ${this.referralLink?.value || ''}`);
    window.open(`mailto:?subject=${subject}&body=${body}`, '_blank');
  }

  async redeemProduct(productId) {
    if (!this.customerData || !this.options.customerId) {
      alert('Vous devez être connecté pour échanger des points.');
      return;
    }

    const product = this.exchangeableProducts.find(p => p.id === productId);
    if (!product) return;

    if (this.customerData.points < product.pointsCost) {
      alert(`Vous n'avez pas assez de points. Il vous manque ${product.pointsCost - this.customerData.points} points.`);
      return;
    }

    if (!confirm(`Êtes-vous sûr de vouloir échanger ${product.pointsCost} points contre "${product.title}" ?`)) {
      return;
    }

    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=client-redeem`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            shop: this.options.shop,
            customerId: this.options.customerId,
            productId: productId
          })
        }
      );

      const result = await response.json();

      if (response.ok && result.success) {
        // Mettre à jour les points du client
        this.customerData.points = result.remainingPoints;
        this.updateCustomerInfo();
        this.updatePointsPreview();
        this.renderExchangeableProducts(); // Rafraîchir la liste

        alert(`🎉 ${result.message}\nPoints restants: ${result.remainingPoints}`);
      } else {
        alert(`Erreur: ${result.error || 'Impossible d\'échanger le produit'}`);
      }
    } catch (error) {
      console.error('Erreur lors de l\'échange:', error);
      alert('Une erreur est survenue lors de l\'échange. Veuillez réessayer.');
    }
  }

  open() {
    if (this.isOpen) return;

    this.isOpen = true;
    this.panel.classList.add('active');
    this.overlay.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Animation du bouton
    this.trigger.style.transform = 'scale(0.9)';
    setTimeout(() => {
      this.trigger.style.transform = '';
    }, 150);
  }

  close() {
    if (!this.isOpen) return;

    this.isOpen = false;
    this.panel.classList.remove('active');
    this.overlay.classList.remove('active');
    document.body.style.overflow = '';
  }

  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  openRewards() {
    this.currentView = 'rewards';
    this.renderRewardsView();
  }

  openHistory() {
    this.currentView = 'history';
    this.renderHistoryView();
  }

  backToMain() {
    this.currentView = 'main';
    this.renderMainView();
  }

  renderMainView() {
    // Afficher le contenu principal et masquer les autres vues
    const mainContent = this.panel.querySelector('.loyalty-main-content');
    const rewardsContent = this.panel.querySelector('.loyalty-rewards-content');
    const historyContent = this.panel.querySelector('.loyalty-history-content');

    if (mainContent) mainContent.style.display = 'block';
    if (rewardsContent) rewardsContent.style.display = 'none';
    if (historyContent) historyContent.style.display = 'none';
  }

  renderRewardsView() {
    // Masquer le contenu principal et afficher les récompenses
    const mainContent = this.panel.querySelector('.loyalty-main-content');
    const rewardsContent = this.panel.querySelector('.loyalty-rewards-content');
    const historyContent = this.panel.querySelector('.loyalty-history-content');

    if (mainContent) mainContent.style.display = 'none';
    if (historyContent) historyContent.style.display = 'none';

    if (!rewardsContent) {
      this.createRewardsView();
    } else {
      rewardsContent.style.display = 'block';
    }
  }

  renderHistoryView() {
    // Masquer le contenu principal et afficher l'historique
    const mainContent = this.panel.querySelector('.loyalty-main-content');
    const rewardsContent = this.panel.querySelector('.loyalty-rewards-content');
    const historyContent = this.panel.querySelector('.loyalty-history-content');

    if (mainContent) mainContent.style.display = 'none';
    if (rewardsContent) rewardsContent.style.display = 'none';

    if (!historyContent) {
      this.createHistoryView();
    } else {
      historyContent.style.display = 'block';
    }
  }

  createRewardsView() {
    const rewardsContent = document.createElement('div');
    rewardsContent.className = 'loyalty-rewards-content';
    rewardsContent.innerHTML = `
      <div class="loyalty-view-header">
        <button class="loyalty-back-btn" onclick="loyaltyWidget.backToMain()">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          Retour
        </button>
        <h3>Récompenses disponibles</h3>
      </div>
      <div class="loyalty-rewards-list">
        ${this.renderRewardsList()}
      </div>
    `;

    this.panel.querySelector('.loyalty-content').appendChild(rewardsContent);
  }

  createHistoryView() {
    const historyContent = document.createElement('div');
    historyContent.className = 'loyalty-history-content';
    historyContent.innerHTML = `
      <div class="loyalty-view-header">
        <button class="loyalty-back-btn" onclick="loyaltyWidget.backToMain()">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          Retour
        </button>
        <h3>Historique des points</h3>
      </div>
      <div class="loyalty-history-list">
        ${this.renderHistoryList()}
      </div>
    `;

    this.panel.querySelector('.loyalty-content').appendChild(historyContent);
  }

  renderRewardsList() {
    if (!this.redeemWays.length) {
      return '<div class="loyalty-empty-state">Aucune récompense disponible pour le moment.</div>';
    }

    const currentPoints = this.customerData?.points || 0;

    return this.redeemWays.map(reward => {
      const canAfford = currentPoints >= reward.pointsCost;
      const statusClass = canAfford ? 'available' : 'unavailable';

      return `
        <div class="loyalty-reward-item ${statusClass}">
          <div class="loyalty-reward-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
            </svg>
          </div>
          <div class="loyalty-reward-content">
            <div class="loyalty-reward-title">${reward.name}</div>
            <div class="loyalty-reward-description">${reward.description}</div>
            <div class="loyalty-reward-cost">${formatCompactNumber(reward.pointsCost)} points</div>
          </div>
          <div class="loyalty-reward-action">
            ${canAfford
              ? '<button class="loyalty-btn loyalty-btn-primary loyalty-btn-sm">Échanger</button>'
              : `<span class="loyalty-points-needed">Il vous manque ${formatCompactNumber(reward.pointsCost - currentPoints)} points</span>`
            }
          </div>
        </div>
      `;
    }).join('');
  }

  renderHistoryList() {
    const history = this.customerData?.recentHistory || [];

    if (!history.length) {
      return '<div class="loyalty-empty-state">Aucun historique disponible.</div>';
    }

    return history.map(item => {
      const isPositive = item.points > 0;
      const pointsClass = isPositive ? 'positive' : 'negative';
      const pointsPrefix = isPositive ? '+' : '';

      return `
        <div class="loyalty-history-item">
          <div class="loyalty-history-icon ${pointsClass}">
            ${isPositive
              ? '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 5V19M5 12L19 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
              : '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
            }
          </div>
          <div class="loyalty-history-content">
            <div class="loyalty-history-description">${item.description}</div>
            <div class="loyalty-history-date">${new Date(item.timestamp).toLocaleDateString('fr-FR')}</div>
          </div>
          <div class="loyalty-history-points ${pointsClass}">
            ${pointsPrefix}${formatCompactNumber(item.points)}
          </div>
        </div>
      `;
    }).join('');
  }

  async signupToLoyalty() {
    if (!this.options.customerId) {
      console.error('Customer ID required for signup');
      return;
    }

    try {
      const response = await fetch(`${this.apiBaseUrl}?prepath=signup&shop=${this.options.shop}&logged_in_customer_id=${this.options.customerId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const result = await response.json();

        // Mettre à jour les données client
        this.customerData = result.customer;
        this.showMemberState();
        this.updatePointsPreview();

        // Afficher un message de succès
        this.showSuccessMessage(result.message);
      } else {
        throw new Error('Erreur lors de l\'inscription');
      }
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error);
      this.showErrorMessage('Impossible de vous inscrire au programme. Veuillez réessayer.');
    }
  }

  showSuccessMessage(message) {
    // Créer une notification temporaire
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000000;
      font-family: inherit;
      font-size: 14px;
      max-width: 300px;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Supprimer après 5 secondes
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  showErrorMessage(message) {
    // Créer une notification d'erreur temporaire
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #f44336;
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000000;
      font-family: inherit;
      font-size: 14px;
      max-width: 300px;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Supprimer après 5 secondes
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  reload() {
    this.loadCustomerData();
  }

  handleResize() {
    if (!this.options.showOnMobile && window.innerWidth <= 768) {
      this.container.style.display = 'none';
    } else {
      this.container.style.display = '';
    }
  }

  hasSeenWidget() {
    return localStorage.getItem('loyalty-widget-seen') === 'true';
  }

  markWidgetSeen() {
    localStorage.setItem('loyalty-widget-seen', 'true');
  }

  // API publique
  updateCustomer(customerId) {
    this.options.customerId = customerId;
    this.loadCustomerData();
  }

  updatePoints(points) {
    if (this.customerData) {
      this.customerData.points = points;
      this.updatePointsPreview();
      this.updateProgress();
    }
  }
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', function() {
  // Vérifier si le widget existe
  if (document.getElementById('loyalty-widget')) {
    // Récupérer les options depuis la configuration Liquid
    const config = window.loyaltyWidgetConfig || {};

    const options = {
      shop: config.shop || '',
      customerId: config.customerId || null,
      position: config.position || 'bottom-right',
      primaryColor: config.primaryColor || '#2E7D32',
      secondaryColor: config.secondaryColor || '#4CAF50',
      showOnMobile: config.showOnMobile !== false,
      autoOpen: config.autoOpen || false
    };

    // Créer l'instance globale
    window.loyaltyWidget = new LoyaltyWidget(options);
  }
});
